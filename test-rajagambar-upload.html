<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Test RajaGambarUpload Component</title>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .raja-gambar-upload-container { border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
        .raja-gambar-preview { margin-top: 15px; }
        .raja-gambar-item { 
            display: inline-block; 
            position: relative; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            overflow: hidden;
        }
        .raja-gambar-item img { width: 150px; height: 150px; object-fit: cover; }
        .info { 
            position: absolute; 
            bottom: 0; 
            left: 0; 
            right: 0; 
            background: rgba(0,0,0,0.7); 
            color: white; 
            padding: 5px; 
            font-size: 12px;
        }
        .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: red;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test RajaGambarUpload Component</h1>
        
        <div id="error-log" class="error" style="display: none;">
            <h3>JavaScript Errors:</h3>
            <ul id="error-list"></ul>
        </div>
        
        <div id="success-log" class="success" style="display: none;">
            Component loaded successfully without errors!
        </div>
        
        <!-- Test Component -->
        <div class="raja-gambar-upload-container" x-data="rajaGambarUpload({
            statePath: 'test_field',
            isMultiple: false,
            collection: 'test',
            maxFileSize: 10,
            acceptedTypes: 'image/jpeg,image/png,image/gif,image/webp',
            enablePicker: true,
            enableUploader: true,
            previewSize: 150,
            showFileName: true,
            showFileSize: true,
            byUser: false,
            byUserId: null,
            convertWebp: false,
            currentValue: null
        })">
            
            <h3>RajaGambarUpload Test</h3>
            <p>Testing shouldShowFileName() and shouldShowFileSize() functions</p>
            
            <!-- Test the functions -->
            <div>
                <p>Show File Name: <span x-text="shouldShowFileName()"></span></p>
                <p>Show File Size: <span x-text="shouldShowFileSize()"></span></p>
                <p>Combined Test: <span x-text="shouldShowFileName() || shouldShowFileSize()"></span></p>
            </div>
            
            <!-- Preview area (will show if value exists) -->
            <div class="raja-gambar-preview" x-show="value && (isMultiple ? value.length > 0 : true)">
                <template x-if="!isMultiple && value">
                    <div class="raja-gambar-item">
                        <img src="/noimage.jpg" alt="Test Image" />
                        <div class="info" x-show="shouldShowFileName() || shouldShowFileSize()">
                            <div x-show="shouldShowFileName()" x-text="'Test File Name'"></div>
                            <div x-show="shouldShowFileSize()" x-text="'Test File Size'"></div>
                        </div>
                        <button type="button" class="remove-btn" @click="value = null">×</button>
                    </div>
                </template>
            </div>
            
            <!-- Test button -->
            <button type="button" @click="value = 'test-123'" style="margin-top: 10px; padding: 10px; background: #007cba; color: white; border: none; border-radius: 4px;">
                Test Preview (Set Value)
            </button>
            
        </div>
    </div>

    <script>
    // Error logging
    window.addEventListener('error', function(e) {
        const errorLog = document.getElementById('error-log');
        const errorList = document.getElementById('error-list');
        const successLog = document.getElementById('success-log');
        
        errorLog.style.display = 'block';
        successLog.style.display = 'none';
        
        const li = document.createElement('li');
        li.textContent = e.message + ' (Line: ' + e.lineno + ')';
        errorList.appendChild(li);
    });

    // RajaGambarUpload function (simplified for testing)
    function rajaGambarUpload(config) {
        return {
            statePath: config.statePath,
            isMultiple: config.isMultiple,
            collection: config.collection,
            maxFileSize: config.maxFileSize,
            acceptedTypes: config.acceptedTypes,
            enablePicker: config.enablePicker,
            enableUploader: config.enableUploader,
            previewSize: config.previewSize,
            showFileName: config.showFileName,
            showFileSize: config.showFileSize,
            byUser: config.byUser,
            byUserId: config.byUserId,
            convertWebp: config.convertWebp,
            currentValue: config.currentValue,
            
            // State
            value: config.currentValue || (config.isMultiple ? [] : null),
            
            init() {
                console.log('RajaGambarUpload initialized successfully');
                console.log('showFileName:', this.showFileName);
                console.log('showFileSize:', this.showFileSize);
                
                // Show success if no errors after 1 second
                setTimeout(() => {
                    const errorLog = document.getElementById('error-log');
                    const successLog = document.getElementById('success-log');
                    if (errorLog.style.display === 'none') {
                        successLog.style.display = 'block';
                    }
                }, 1000);
            },
            
            shouldShowFileName() {
                return this.showFileName;
            },
            
            shouldShowFileSize() {
                return this.showFileSize;
            }
        }
    }
    </script>
</body>
</html>
