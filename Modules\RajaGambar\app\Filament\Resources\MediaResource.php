<?php

namespace Modules\RajaGambar\Filament\Resources;

use Modules\RajaGambar\Filament\Resources\MediaResource\Pages;
use Modules\RajaGambar\Models\Media;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class MediaResource extends Resource
{
    protected static ?string $model = Media::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Media Raja Gambar';

    protected static ?string $modelLabel = 'Media';

    protected static ?string $pluralModelLabel = 'Media';

    protected static ?string $navigationGroup = 'Raja Gambar';
    protected static ?string $slug = 'rajagambar/media';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make()
                    ->schema([
                        Section::make('Informasi Media')
                            ->schema([
                                TextInput::make('name')
                                    ->label('Nama File')
                                    ->required(),
                                FileUpload::make('file_name')
                                    ->label('File')
                                    ->required(),
                            ]),

                        Section::make('Pengaturan')
                            ->schema([
                                Select::make('collection_name')
                                    ->label('Koleksi')
                                    ->options([
                                        'rajagambar' => 'Raja Gambar',
                                        'default' => 'Default',
                                        'gallery' => 'Gallery',
                                        'documents' => 'Dokumen',
                                        'avatars' => 'Avatar',
                                        'banners' => 'Banner',
                                        'products' => 'Produk',
                                        'cms' => 'CMS',
                                    ])
                                    ->default('rajagambar')
                                    ->required(),
                            ]),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(false)
            ->contentGrid([
                'sm' => 1,
                'md' => 2,
                'lg' => 3,
                'xl' => 4,
                '2xl' => 5,
            ])
            ->columns([
                Stack::make([
                    // Card container dengan border dan shadow
                    Stack::make([
                        // Preview image dengan aspect ratio yang konsisten
                        ImageColumn::make('preview')
                            ->label('')
                            ->height(200)
                            ->width('100%')
                            ->getStateUsing(function ($record) {
                                if (!$record->isImage()) {
                                    // Return icon untuk non-image files
                                    return match (true) {
                                        str_contains($record->mime_type, 'pdf') => asset('icons/pdf-icon.png'),
                                        str_contains($record->mime_type, 'video') => asset('icons/video-icon.png'),
                                        str_contains($record->mime_type, 'audio') => asset('icons/audio-icon.png'),
                                        default => asset('icons/file-icon.png'),
                                    };
                                }

                                $prefix = config('media-library.prefix', 'uploads');
                                $disk = $record->disk ?? 'public';

                                // Nama thumbnail
                                $thumbName = pathinfo($record->file_name, PATHINFO_FILENAME) . '_thumb.webp';
                                $relativeThumb = $record->collection_name . '/' . $thumbName;

                                if (Storage::disk($disk)->exists($prefix . '/' . $relativeThumb)) {
                                    return Storage::url($prefix . '/' . $relativeThumb);
                                }

                                // Fallback ke file asli
                                return $record->url;
                            })
                            ->defaultImageUrl(asset('icons/no-image.png'))
                            ->extraAttributes([
                                'class' => 'rounded-t-lg object-cover w-full',
                                'style' => 'aspect-ratio: 16/9;'
                            ]),

                        // Content area dengan padding
                        Stack::make([
                            // Title dengan truncate
                            TextColumn::make('name')
                                ->label('')
                                ->weight('bold')
                                ->size('sm')
                                ->searchable()
                                ->sortable()
                                ->limit(30)
                                ->tooltip(fn ($record) => $record->name),

                            // File info row
                            Stack::make([
                                TextColumn::make('file_name')
                                    ->label('')
                                    ->size('xs')
                                    ->color('gray')
                                    ->limit(25)
                                    ->tooltip(fn ($record) => $record->file_name),

                                TextColumn::make('human_readable_size')
                                    ->label('')
                                    ->size('xs')
                                    ->color('gray')
                                    ->weight('medium'),
                            ])->space(1),

                            // Collection badge
                            TextColumn::make('collection_name')
                                ->label('')
                                ->badge()
                                ->size('xs')
                                ->color(fn (string $state): string => match ($state) {
                                    'rajagambar' => 'success',
                                    'gallery' => 'info',
                                    'documents' => 'warning',
                                    'avatars' => 'danger',
                                    'banners' => 'primary',
                                    'products' => 'secondary',
                                    'cms' => 'gray',
                                    default => 'gray',
                                }),

                            // Date and user info
                            Stack::make([
                                TextColumn::make('created_at')
                                    ->label('')
                                    ->size('xs')
                                    ->color('gray')
                                    ->dateTime('d M Y'),

                                TextColumn::make('user.name')
                                    ->label('')
                                    ->size('xs')
                                    ->color('gray')
                                    ->default('System')
                                    ->prefix('oleh '),
                            ])->space(1),
                        ])
                        ->space(2)
                        ->extraAttributes(['class' => 'p-4']),
                    ])
                    ->extraAttributes([
                        'class' => 'bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200 overflow-hidden'
                    ]),
                ])
                ->space(0),
            ])
            ->filters([
                SelectFilter::make('collection_name')
                    ->label('Koleksi')
                    ->multiple()
                    ->options([
                        'rajagambar' => 'Raja Gambar',
                        'default' => 'Default',
                        'gallery' => 'Gallery',
                        'documents' => 'Dokumen',
                        'avatars' => 'Avatar',
                        'banners' => 'Banner',
                        'products' => 'Produk',
                        'cms' => 'CMS',
                    ])
                    ->searchable(),

                SelectFilter::make('file_type')
                    ->label('Tipe File')
                    ->options([
                        'image' => 'Gambar',
                        'video' => 'Video',
                        'audio' => 'Audio',
                        'document' => 'Dokumen',
                        'other' => 'Lainnya',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $value): Builder => match ($value) {
                                'image' => $query->where('mime_type', 'LIKE', 'image/%'),
                                'video' => $query->where('mime_type', 'LIKE', 'video/%'),
                                'audio' => $query->where('mime_type', 'LIKE', 'audio/%'),
                                'document' => $query->whereIn('mime_type', [
                                    'application/pdf',
                                    'application/msword',
                                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                ]),
                                'other' => $query->where('mime_type', 'NOT LIKE', 'image/%')
                                    ->where('mime_type', 'NOT LIKE', 'video/%')
                                    ->where('mime_type', 'NOT LIKE', 'audio/%')
                                    ->whereNotIn('mime_type', [
                                        'application/pdf',
                                        'application/msword',
                                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                                    ]),
                                default => $query,
                            }
                        );
                    }),

                SelectFilter::make('user_id')
                    ->label('Diupload oleh')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                Filter::make('created_at')
                    ->form([
                        DatePicker::make('created_from')
                            ->label('Dari Tanggal'),
                        DatePicker::make('created_until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['created_from'] ?? null) {
                            $indicators[] = 'Dari: ' . Carbon::parse($data['created_from'])->format('d M Y');
                        }

                        if ($data['created_until'] ?? null) {
                            $indicators[] = 'Sampai: ' . Carbon::parse($data['created_until'])->format('d M Y');
                        }

                        return $indicators;
                    }),
            ], layout: Tables\Enums\FiltersLayout::AboveContentCollapsible)
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Action::make('view')
                        ->label('Lihat')
                        ->icon('heroicon-o-eye')
                        ->color('info')
                        ->url(fn(Media $record): string => $record->url ?? '#')
                        ->openUrlInNewTab()
                        ->visible(fn(Media $record): bool => $record->isImage()),

                    Action::make('download')
                        ->label('Download')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('success')
                        ->url(fn(Media $record): string => $record->url ?? '#')
                        ->openUrlInNewTab(),

                    Tables\Actions\EditAction::make()
                        ->label('Edit')
                        ->color('warning'),

                    Tables\Actions\ViewAction::make()
                        ->label('Detail')
                        ->color('gray'),

                    Tables\Actions\DeleteAction::make()
                        ->label('Hapus')
                        ->color('danger'),
                ])
                ->label('Aksi')
                ->icon('heroicon-m-ellipsis-vertical')
                ->size('sm')
                ->color('gray')
                ->button(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->requiresConfirmation()
                        ->modalHeading('Hapus Media Terpilih')
                        ->modalDescription('Apakah Anda yakin ingin menghapus media yang dipilih? Tindakan ini tidak dapat dibatalkan.')
                        ->modalSubmitActionLabel('Ya, Hapus'),
                ])
                ->label('Aksi Massal'),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s')
            ->deferLoading()
            ->striped()
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->extremePaginationLinks()
            ->searchOnBlur()
            ->persistSearchInSession()
            ->persistColumnSearchesInSession()
            ->persistFiltersInSession()
            ->persistSortInSession();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMedia::route('/'),
            'create' => Pages\CreateMedia::route('/create'),
            'edit' => Pages\EditMedia::route('/{record}/edit'),
            'view' => Pages\ViewMedia::route('/{record}'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return number_format(static::getModel()::count());
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user'])
            ->orderBy('created_at', 'desc');
    }
}
