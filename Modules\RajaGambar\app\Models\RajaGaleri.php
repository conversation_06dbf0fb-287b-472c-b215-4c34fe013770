<?php

namespace Modules\RajaGambar\Models;

use <PERSON><PERSON><PERSON>\RajaJson\Models\Rajajson;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Image\Enums\Fit;

class <PERSON><PERSON><PERSON><PERSON> extends <PERSON><PERSON><PERSON> implements HasMedia
{
    use InteractsWithMedia;

    /**
     * Boot method untuk set global scope dan auto-assign jenis
     */
    protected static function boot()
    {
        parent::boot();

        // Add global scope untuk filter berdasarkan jenis
        static::addGlobalScope('jenis', function (\Illuminate\Database\Eloquent\Builder $builder) {
            $builder->where('jenis', 'GALERI');
        });

        // Auto-assign jenis saat creating
        static::creating(function ($model) {
            $model->jenis = 'GALERI';
        });

        // Auto-assign jenis saat updating
        static::updating(function ($model) {
            $model->jenis = 'GALERI';
        });
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('default')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])
            ->singleFile();

        $this->addMediaCollection('gallery')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

        $this->addMediaCollection('avatars')
            ->acceptsMimeTypes(['image/jpeg', 'image/png'])
            ->singleFile();

        $this->addMediaCollection('banners')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('products')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('cms')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']);

        $this->addMediaCollection('rajagambar')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        // Thumbnail conversion untuk semua gambar
        $this->addMediaConversion('thumb')
            ->fit(Fit::Crop, 300, 300)
            ->quality(80)
            ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms', 'rajagambar')
            ->nonQueued();

        // Small conversion untuk preview
        $this->addMediaConversion('small')
            ->fit(Fit::Contain, 150, 150)
            ->quality(70)
            ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms', 'rajagambar')
            ->nonQueued();

        // Medium conversion untuk display
        $this->addMediaConversion('medium')
            ->fit(Fit::Contain, 600, 600)
            ->quality(85)
            ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms', 'rajagambar')
            ->nonQueued();

        // Large conversion untuk full view
        $this->addMediaConversion('large')
            ->fit(Fit::Contain, 1200, 1200)
            ->quality(90)
            ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms', 'rajagambar')
            ->nonQueued();

        // WebP conversions untuk optimasi
        $this->addMediaConversion('webp')
            ->format('webp')
            ->quality(80)
            ->performOnCollections('default', 'gallery', 'banners', 'products', 'cms', 'rajagambar')
            ->nonQueued();

        // Avatar specific conversions
        $this->addMediaConversion('avatar')
            ->fit(Fit::Crop, 100, 100)
            ->quality(85)
            ->performOnCollections('avatars')
            ->nonQueued();

        // Banner specific conversions
        $this->addMediaConversion('banner_thumb')
            ->fit(Fit::Crop, 400, 200)
            ->quality(85)
            ->performOnCollections('banners')
            ->nonQueued();
    }

    /**
     * Get media URL with fallback
     */
    public function getMediaUrl($collection = 'default', $conversion = ''): ?string
    {
        $media = $this->getFirstMedia($collection);
        
        if (!$media) {
            return null;
        }

        try {
            return $conversion ? $media->getUrl($conversion) : $media->getUrl();
        } catch (\Exception $e) {
            // Fallback ke URL asli jika conversion gagal
            return $media->getUrl();
        }
    }

    /**
     * Get all media in collection
     */
    public function getAllMediaInCollection($collection = 'default')
    {
        return $this->getMedia($collection);
    }

    /**
     * Check if has media in collection
     */
    public function hasMediaInCollection($collection = 'default'): bool
    {
        return $this->getMedia($collection)->isNotEmpty();
    }

    /**
     * Get media count in collection
     */
    public function getMediaCountInCollection($collection = 'default'): int
    {
        return $this->getMedia($collection)->count();
    }

    /**
     * Delete all media in collection
     */
    public function clearMediaCollection(string $collectionName = 'default'): \Spatie\MediaLibrary\HasMedia
    {
        $this->getMedia($collectionName)->each->delete();

        return $this;
    }

    /**
     * Get thumbnail URL
     */
    public function getThumbnailUrl($collection = 'default'): ?string
    {
        return $this->getMediaUrl($collection, 'thumb');
    }

    /**
     * Get small image URL
     */
    public function getSmallImageUrl($collection = 'default'): ?string
    {
        return $this->getMediaUrl($collection, 'small');
    }

    /**
     * Get medium image URL
     */
    public function getMediumImageUrl($collection = 'default'): ?string
    {
        return $this->getMediaUrl($collection, 'medium');
    }

    /**
     * Get large image URL
     */
    public function getLargeImageUrl($collection = 'default'): ?string
    {
        return $this->getMediaUrl($collection, 'large');
    }

    /**
     * Get WebP URL
     */
    public function getWebpUrl($collection = 'default'): ?string
    {
        return $this->getMediaUrl($collection, 'webp');
    }

    /**
     * Get avatar URL
     */
    public function getAvatarUrl(): ?string
    {
        return $this->getMediaUrl('avatars', 'avatar');
    }

    /**
     * Get banner thumbnail URL
     */
    public function getBannerThumbnailUrl(): ?string
    {
        return $this->getMediaUrl('banners', 'banner_thumb');
    }

    /**
     * Scope untuk filter berdasarkan collection
     */
    public function scopeWithMediaInCollection($query, $collection)
    {
        return $query->whereHas('media', function ($q) use ($collection) {
            $q->where('collection_name', $collection);
        });
    }

    /**
     * Scope untuk filter berdasarkan user
     */
    public function scopeByUser($query, $userId = null)
    {
        $userId = $userId ?? \Illuminate\Support\Facades\Auth::id();
        return $query->whereHas('media', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }
}
