@php
    $statePath = $getStatePath();
    $state = $getState();
    $isMultiple = $isMultiple();
    $collection = $getCollection();
    $maxFileSize = $getMaxFileSize();
    $acceptedTypes = $getAcceptedTypesString();
    $previewSize = $getPreviewSize();
    $placeholder = $getPlaceholder() ?? 'Pilih atau upload gambar';
@endphp

@push('styles')
<style>
.raja-gambar-upload-container {
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.raja-gambar-upload-container:hover {
    border-color: #3b82f6;
    background: #f8faff;
}

.raja-gambar-upload-container.dragover {
    border-color: #10b981;
    background: #f0fdf4;
}

.raja-gambar-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.raja-gambar-item {
    position: relative;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.raja-gambar-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.raja-gambar-item .info {
    padding: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.raja-gambar-item .remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.raja-gambar-upload-area {
    text-align: center;
    padding: 2rem;
    cursor: pointer;
}

.raja-gambar-upload-area:hover {
    background: #f3f4f6;
}

.raja-gambar-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.raja-gambar-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.raja-gambar-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.raja-gambar-btn.primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.raja-gambar-btn.primary:hover {
    background: #2563eb;
}

.raja-gambar-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.raja-gambar-modal-content {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    width: 800px;
}

.raja-gambar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.raja-gambar-grid-item {
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
}

.raja-gambar-grid-item:hover {
    border-color: #3b82f6;
}

.raja-gambar-grid-item.selected {
    border-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.raja-gambar-grid-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.raja-gambar-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.raja-gambar-pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    background: white;
    cursor: pointer;
    border-radius: 4px;
}

.raja-gambar-pagination button:hover {
    background: #f3f4f6;
}

.raja-gambar-pagination button.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.upload-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #10b981;
    transition: width 0.3s ease;
}
</style>
@endpush

@push('scripts')
<script>
function rajaGambarUpload(config) {
    return {
        statePath: config.statePath,
        isMultiple: config.isMultiple,
        collection: config.collection,
        maxFileSize: config.maxFileSize,
        acceptedTypes: config.acceptedTypes,
        enablePicker: config.enablePicker,
        enableUploader: config.enableUploader,
        previewSize: config.previewSize,
        showFileName: config.showFileName,
        showFileSize: config.showFileSize,
        byUser: config.byUser,
        byUserId: config.byUserId,
        convertWebp: config.convertWebp,
        currentValue: config.currentValue,
        
        // State
        value: config.currentValue || (config.isMultiple ? [] : null),
        showModal: false,
        mediaItems: [],
        selectedItems: [],
        currentPage: 1,
        totalPages: 1,
        loading: false,
        uploading: false,
        uploadProgress: 0,

        // Media data (sama seperti RajaPicker)
        selectedMedia: null,
        selectedMediaList: [],
        
        init() {
            this.loadCurrentMedia();
            this.$watch('value', (newValue) => {
                this.$wire.set(this.statePath, newValue);
            });

            // Setup drag and drop
            this.setupDragDrop();
        },

        // Load current media data (sama seperti RajaPicker)
        async loadCurrentMedia() {
            if (!this.value) return;

            try {
                if (this.isMultiple && Array.isArray(this.value)) {
                    // Multiple media - load by IDs
                    if (this.value.length > 0) {
                        const response = await fetch(`/api/media/by-ids?ids=${this.value.join(',')}`);
                        if (response.ok) {
                            this.selectedMediaList = await response.json();
                        }
                    }
                } else if (this.value) {
                    // Single media - load by ID
                    const response = await fetch(`/api/media/${this.value}`);
                    if (response.ok) {
                        this.selectedMedia = await response.json();
                    }
                }
            } catch (error) {
                console.error('Error loading current media:', error);
            }
        },
        
        setupDragDrop() {
            const container = this.$refs.uploadContainer;
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, this.preventDefaults, false);
            });
            
            ['dragenter', 'dragover'].forEach(eventName => {
                container.addEventListener(eventName, () => {
                    container.classList.add('dragover');
                }, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, () => {
                    container.classList.remove('dragover');
                }, false);
            });
            
            container.addEventListener('drop', this.handleDrop.bind(this), false);
        },
        
        preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        },
        
        handleDrop(e) {
            const files = e.dataTransfer.files;
            this.handleFiles(files);
        },
        
        handleFileSelect(event) {
            const files = event.target.files;
            this.handleFiles(files);
        },
        
        async handleFiles(files) {
            if (!this.enableUploader) return;
            
            const validFiles = Array.from(files).filter(file => {
                const isValidType = this.acceptedTypes.split(',').some(type => 
                    file.type.includes(type.replace('image/', ''))
                );
                const isValidSize = file.size <= (this.maxFileSize * 1024 * 1024);
                return isValidType && isValidSize;
            });
            
            if (validFiles.length === 0) {
                alert('File tidak valid atau terlalu besar');
                return;
            }
            
            for (const file of validFiles) {
                await this.uploadFile(file);
            }
        },
        
        async uploadFile(file) {
            this.uploading = true;
            this.uploadProgress = 0;
            
            const formData = new FormData();
            formData.append('file', file);
            formData.append('collection', this.collection);
            formData.append('convert_webp', this.convertWebp ? '1' : '0');
            
            try {
                const response = await fetch('/api/media/upload', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                    },
                    onUploadProgress: (progressEvent) => {
                        this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.addToValue(result.media);
                    this.$dispatch('upload-success', result.media);
                } else {
                    alert('Upload gagal: ' + result.message);
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert('Terjadi kesalahan saat upload');
            } finally {
                this.uploading = false;
                this.uploadProgress = 0;
            }
        },
        
        addToValue(media) {
            if (this.isMultiple) {
                if (!Array.isArray(this.value)) {
                    this.value = [];
                }
                this.value.push(media.id);
                this.selectedMediaList.push(media);
            } else {
                this.value = media.id;
                this.selectedMedia = media;
            }
        },
        
        removeItem(index) {
            if (this.isMultiple) {
                this.value.splice(index, 1);
                this.selectedMediaList.splice(index, 1);
            } else {
                this.value = null;
                this.selectedMedia = null;
            }
        },
        
        async openPicker() {
            if (!this.enablePicker) return;
            
            this.showModal = true;
            this.loading = true;
            
            try {
                await this.loadMediaItems();
            } catch (error) {
                console.error('Error loading media:', error);
            } finally {
                this.loading = false;
            }
        },
        
        async loadMediaItems(page = 1) {
            const params = new URLSearchParams({
                page: page,
                collection: this.collection,
                per_page: 12
            });
            
            if (this.byUser) {
                params.append('by_user', '1');
            }
            
            if (this.byUserId) {
                params.append('user_id', this.byUserId);
            }
            
            const response = await fetch(`/api/media/all-images?${params}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                }
            });
            
            const data = await response.json();
            this.mediaItems = data.data;
            this.currentPage = data.current_page;
            this.totalPages = data.last_page;
        },
        
        selectMedia(media) {
            if (this.isMultiple) {
                const index = this.selectedItems.findIndex(item => item.id === media.id);
                if (index > -1) {
                    this.selectedItems.splice(index, 1);
                } else {
                    this.selectedItems.push(media);
                }
            } else {
                this.selectedItems = [media];
            }
        },
        
        isSelected(media) {
            return this.selectedItems.some(item => item.id === media.id);
        },
        
        confirmSelection() {
            if (this.isMultiple) {
                this.value = this.selectedItems.map(item => item.id);
                this.selectedMediaList = [...this.selectedItems];
            } else {
                this.value = this.selectedItems[0]?.id || null;
                this.selectedMedia = this.selectedItems[0] || null;
            }

            this.closeModal();
        },
        
        closeModal() {
            this.showModal = false;
            this.selectedItems = [];
        },
        
        async changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.loading = true;
                await this.loadMediaItems(page);
                this.loading = false;
            }
        },
        
        getMediaUrl(media) {
            return media.original_url || media.url || '{{ config('rajagambar.raja_gambar_upload.placeholder_image', '/noimage.jpg') }}';
        },



        // Add /storage/ prefix to URL for display (sama seperti RajaPicker)
        addStoragePrefix(url) {
            if (!url || typeof url !== 'string') return url;

            // Don't add prefix if URL already has it or is a full URL
            if (url.startsWith('/storage/') || url.startsWith('http')) {
                return url;
            }

            // Add /storage/ prefix
            return '/storage/' + url.replace(/^\/+/, '');
        },

        getMediaName(media) {
            return media.name || media.file_name || 'Unnamed';
        },

        // Format file size (sama seperti RajaPicker)
        formatFileSize(bytes) {
            if (!bytes) return '';
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            if (bytes === 0) return '0 Bytes';
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        },
        
        getMediaSize(media) {
            if (!media.size) return '';
            const size = media.size;
            if (size < 1024) return size + ' B';
            if (size < 1024 * 1024) return Math.round(size / 1024) + ' KB';
            return Math.round(size / (1024 * 1024)) + ' MB';
        },

        shouldShowFileName() {
            return this.showFileName;
        },

        shouldShowFileSize() {
            return this.showFileSize;
        }
    }
}
</script>
@endpush

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div class="raja-gambar-upload-container" x-data="rajaGambarUpload({
        statePath: '{{ $statePath }}',
        isMultiple: {{ $isMultiple ? 'true' : 'false' }},
        collection: '{{ $collection }}',
        maxFileSize: {{ $maxFileSize }},
        acceptedTypes: '{{ $acceptedTypes }}',
        enablePicker: {{ $isPickerEnabled() ? 'true' : 'false' }},
        enableUploader: {{ $isUploaderEnabled() ? 'true' : 'false' }},
        previewSize: {{ $previewSize }},
        showFileName: {{ $shouldShowFileName() ? 'true' : 'false' }},
        showFileSize: {{ $shouldShowFileSize() ? 'true' : 'false' }},
        byUser: {{ $isByUser() ? 'true' : 'false' }},
        byUserId: {{ $getByUserId() ?? 'null' }},
        convertWebp: {{ $shouldConvertWebp() ? 'true' : 'false' }},
        currentValue: @js($state)
    })" x-ref="uploadContainer">
        
        <!-- Hidden input untuk menyimpan nilai -->
        <input
            type="hidden"
            name="{{ $statePath }}"
            {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
            x-model="value"
        />
        
        <!-- Upload Area -->
        <div class="raja-gambar-upload-area" @click="$refs.fileInput.click()" x-show="!value || (isMultiple && (!value || value.length === 0))">
            <div class="text-gray-500 mb-2">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <p class="text-sm text-gray-600">{{ $placeholder }}</p>
            <p class="text-xs text-gray-400 mt-1">
                Drag & drop atau klik untuk memilih file
                <br>
                Max: {{ $maxFileSize }}MB | Format: {{ str_replace(['image/', ','], ['', ', '], $acceptedTypes) }}
            </p>
        </div>
        
        <!-- File Input -->
        <input
            type="file"
            x-ref="fileInput"
            @change="handleFileSelect($event)"
            :accept="acceptedTypes"
            :multiple="isMultiple"
            style="display: none;"
        />
        
        <!-- Action Buttons -->
        <div class="raja-gambar-buttons" x-show="enablePicker || enableUploader">
            <button type="button" class="raja-gambar-btn" @click="$refs.fileInput.click()" x-show="enableUploader">
                📁 Upload File
            </button>
            <button type="button" class="raja-gambar-btn" @click="openPicker()" x-show="enablePicker">
                🖼️ Pilih dari Galeri
            </button>
        </div>
        
        <!-- Upload Progress -->
        <div class="upload-progress" x-show="uploading">
            <div class="text-sm text-gray-600 mb-2">Uploading... <span x-text="uploadProgress + '%'"></span></div>
            <div class="progress-bar">
                <div class="progress-fill" :style="'width: ' + uploadProgress + '%'"></div>
            </div>
        </div>
        
        <!-- Preview Area -->
        <div class="raja-gambar-preview" x-show="(isMultiple ? selectedMediaList.length > 0 : selectedMedia)">
            <!-- Single Preview -->
            <template x-if="!isMultiple && selectedMedia">
                <div class="raja-gambar-item">
                    <img :src="addStoragePrefix(selectedMedia.url)" :alt="selectedMedia.name" />
                    <div class="info" x-show="shouldShowFileName() || shouldShowFileSize()">
                        <div x-show="shouldShowFileName()" x-text="selectedMedia.name"></div>
                        <div x-show="shouldShowFileSize()" x-text="formatFileSize(selectedMedia.size)"></div>
                    </div>
                    <button type="button" class="remove-btn" @click="removeItem(0)">×</button>
                </div>
            </template>

            <!-- Multiple Preview -->
            <template x-if="isMultiple && selectedMediaList.length > 0">
                <template x-for="(media, index) in selectedMediaList" :key="media.id">
                    <div class="raja-gambar-item">
                        <img :src="addStoragePrefix(media.url)" :alt="media.name" />
                        <div class="info" x-show="shouldShowFileName() || shouldShowFileSize()">
                            <div x-show="shouldShowFileName()" x-text="media.name"></div>
                            <div x-show="shouldShowFileSize()" x-text="formatFileSize(media.size)"></div>
                        </div>
                        <button type="button" class="remove-btn" @click="removeItem(index)">×</button>
                    </div>
                </template>
            </template>
        </div>
        
        <!-- Modal Picker -->
        <div class="raja-gambar-modal" x-show="showModal" @click.self="closeModal()">
            <div class="raja-gambar-modal-content">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">Pilih Media</h3>
                    <button type="button" @click="closeModal()" class="text-gray-500 hover:text-gray-700">×</button>
                </div>
                
                <div x-show="loading" class="text-center py-8">
                    <div class="text-gray-500">Loading...</div>
                </div>
                
                <div x-show="!loading">
                    <div class="raja-gambar-grid">
                        <template x-for="media in mediaItems" :key="media.id">
                            <div class="raja-gambar-grid-item" 
                                 :class="{ 'selected': isSelected(media) }"
                                 @click="selectMedia(media)">
                                <img :src="getMediaUrl(media)" :alt="getMediaName(media)" />
                                <div class="p-2">
                                    <div class="text-xs text-gray-600" x-text="getMediaName(media)"></div>
                                    <div class="text-xs text-gray-400" x-text="getMediaSize(media)"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="raja-gambar-pagination" x-show="totalPages > 1">
                        <button @click="changePage(currentPage - 1)" :disabled="currentPage <= 1">‹ Prev</button>
                        <template x-for="page in Array.from({length: totalPages}, (_, i) => i + 1)" :key="page">
                            <button @click="changePage(page)" 
                                    :class="{ 'active': page === currentPage }"
                                    x-text="page"></button>
                        </template>
                        <button @click="changePage(currentPage + 1)" :disabled="currentPage >= totalPages">Next ›</button>
                    </div>
                    
                    <!-- Modal Actions -->
                    <div class="flex justify-end gap-2 mt-4">
                        <button type="button" class="raja-gambar-btn" @click="closeModal()">Batal</button>
                        <button type="button" class="raja-gambar-btn primary" @click="confirmSelection()" 
                                :disabled="selectedItems.length === 0">
                            Pilih (<span x-text="selectedItems.length"></span>)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-dynamic-component>
