/* Custom CSS untuk Media Grid Layout */

/* Media Card Styling */
.media-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
    @apply hover:shadow-md transition-all duration-200 overflow-hidden;
    @apply transform hover:-translate-y-1;
}

.media-card:hover {
    @apply shadow-lg border-gray-300 dark:border-gray-600;
}

/* Media Preview Image */
.media-preview {
    @apply w-full h-48 object-cover rounded-t-lg;
    @apply bg-gray-100 dark:bg-gray-700;
    aspect-ratio: 16/9;
}

.media-preview-placeholder {
    @apply w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200;
    @apply dark:from-gray-700 dark:to-gray-800;
    @apply flex items-center justify-center rounded-t-lg;
}

.media-preview-icon {
    @apply w-16 h-16 text-gray-400 dark:text-gray-500;
}

/* Media Content Area */
.media-content {
    @apply p-4 space-y-3;
}

.media-title {
    @apply font-semibold text-gray-900 dark:text-white;
    @apply text-sm leading-tight;
    @apply truncate;
}

.media-filename {
    @apply text-xs text-gray-500 dark:text-gray-400;
    @apply truncate;
}

.media-info {
    @apply flex items-center justify-between;
    @apply text-xs text-gray-500 dark:text-gray-400;
}

.media-size {
    @apply font-medium;
}

.media-date {
    @apply text-gray-400 dark:text-gray-500;
}

/* Collection Badge */
.collection-badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.collection-badge.rajagambar {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.collection-badge.gallery {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.collection-badge.documents {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.collection-badge.avatars {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.collection-badge.banners {
    @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.collection-badge.products {
    @apply bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200;
}

.collection-badge.cms {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200;
}

/* Grid Layout Responsive */
.media-grid {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (max-width: 640px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        @apply gap-3;
    }
    
    .media-content {
        @apply p-3 space-y-2;
    }
}

@media (min-width: 1536px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        @apply gap-6;
    }
}

/* Action Button Styling */
.media-actions {
    @apply absolute top-2 right-2 opacity-0 transition-opacity duration-200;
}

.media-card:hover .media-actions {
    @apply opacity-100;
}

.action-button {
    @apply bg-white dark:bg-gray-800 shadow-lg rounded-full p-2;
    @apply hover:bg-gray-50 dark:hover:bg-gray-700;
    @apply transition-colors duration-150;
}

/* Loading States */
.media-skeleton {
    @apply animate-pulse;
}

.media-skeleton .media-preview {
    @apply bg-gray-300 dark:bg-gray-600;
}

.media-skeleton .media-title {
    @apply h-4 bg-gray-300 dark:bg-gray-600 rounded;
}

.media-skeleton .media-filename {
    @apply h-3 bg-gray-200 dark:bg-gray-700 rounded;
}

/* Upload Area Styling */
.upload-area {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600;
    @apply rounded-lg p-8 text-center;
    @apply hover:border-gray-400 dark:hover:border-gray-500;
    @apply transition-colors duration-200;
}

.upload-area.dragover {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900/20;
}

.upload-icon {
    @apply w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-4;
}

.upload-text {
    @apply text-sm text-gray-600 dark:text-gray-400;
}

.upload-hint {
    @apply text-xs text-gray-500 dark:text-gray-500 mt-2;
}

/* Filter and Search Styling */
.filter-tabs {
    @apply border-b border-gray-200 dark:border-gray-700;
}

.filter-tab {
    @apply px-4 py-2 text-sm font-medium rounded-t-lg;
    @apply text-gray-500 dark:text-gray-400;
    @apply hover:text-gray-700 dark:hover:text-gray-300;
    @apply transition-colors duration-150;
}

.filter-tab.active {
    @apply text-blue-600 dark:text-blue-400;
    @apply border-b-2 border-blue-600 dark:border-blue-400;
    @apply bg-blue-50 dark:bg-blue-900/20;
}

.filter-badge {
    @apply ml-2 px-2 py-1 text-xs rounded-full;
    @apply bg-gray-100 dark:bg-gray-700;
    @apply text-gray-600 dark:text-gray-300;
}

/* Notification Styling */
.notification-success {
    @apply bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800;
}

.notification-error {
    @apply bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800;
}

.notification-warning {
    @apply bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800;
}

.notification-info {
    @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .media-card {
        @apply shadow-none border-0 border-b border-gray-200 dark:border-gray-700;
        @apply rounded-none;
    }
    
    .media-preview {
        @apply rounded-none h-40;
    }
    
    .media-actions {
        @apply opacity-100 top-1 right-1;
    }
    
    .action-button {
        @apply p-1.5;
    }
}

/* Print Styles */
@media print {
    .media-card {
        @apply shadow-none border border-gray-300;
        @apply break-inside-avoid;
    }
    
    .media-actions {
        @apply hidden;
    }
}
