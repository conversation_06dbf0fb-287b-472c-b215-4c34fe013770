<?php
// Test upload langsung
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "<h1>Test Upload Direct</h1>";

// Create test PNG file
$testFile = __DIR__ . '/test-upload.png';
$pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
file_put_contents($testFile, $pngData);

echo "<p>Test file created: " . filesize($testFile) . " bytes</p>";

// Simulate upload request
$uploadedFile = new \Illuminate\Http\UploadedFile(
    $testFile,
    'test-upload.png',
    'image/png',
    null,
    true
);

// Create request
$request = new \Illuminate\Http\Request();
$request->files->set('file', $uploadedFile);
$request->request->set('collection', 'test');
$request->request->set('convert_webp', '0');

// Set up session and CSRF
$request->setLaravelSession(app('session'));
$request->session()->regenerateToken();

echo "<p>Request created</p>";

try {
    // Get controller
    $controller = app(\Modules\RajaGambar\app\Http\Controllers\MediaApiController::class);
    
    echo "<p>Controller instantiated</p>";
    
    // Call upload method
    $response = $controller->upload($request);
    
    echo "<h2>Response:</h2>";
    echo "<pre>";
    if ($response instanceof \Illuminate\Http\JsonResponse) {
        echo "Status: " . $response->getStatusCode() . "\n";
        echo "Content: " . $response->getContent() . "\n";
    } else {
        var_dump($response);
    }
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Clean up
if (file_exists($testFile)) {
    unlink($testFile);
}
?>
