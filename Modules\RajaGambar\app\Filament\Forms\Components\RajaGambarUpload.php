<?php

namespace Modules\RajaGambar\Filament\Forms\Components;

use Filament\Forms\Components\Field;


class RajaGambarUpload extends Field
{
    protected string $view = 'rajagambar::components.forms.raja-gambar-upload';

    // Konfigurasi default
    protected array $acceptedFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    protected int $maxFileSize = 10; // MB
    protected string $collection = 'rajagambar';
    protected bool $multiple = false;
    protected ?string $directory = null;
    protected bool $enablePicker = true;
    protected bool $enableUploader = true;
    protected ?string $placeholder = null;
    protected int $previewSize = 150; // px
    protected bool $showFileName = true;
    protected bool $showFileSize = true;
    protected int $perPage = 12;
    protected bool $byUser = false;
    protected ?int $byUserId = null;
    protected bool $convertWebp = false;
    protected bool $enableCrop = false;
    protected array $cropAspectRatio = [1, 1]; // [width, height]
    protected int $cropQuality = 90;
    protected bool $enableResize = false;
    protected ?int $resizeWidth = null;
    protected ?int $resizeHeight = null;
    protected bool $enableWatermark = false;
    protected ?string $watermarkPath = null;
    protected string $watermarkPosition = 'bottom-right';
    protected int $watermarkOpacity = 50;
    protected string $placeholderImage = '/noimage.jpg';

    /**
     * Set accepted file types
     */
    public function acceptedFileTypes(array $types): static
    {
        $this->acceptedFileTypes = $types;
        return $this;
    }

    /**
     * Set max file size in MB
     */
    public function maxFileSize(int $size): static
    {
        $this->maxFileSize = $size;
        return $this;
    }

    /**
     * Set collection name
     */
    public function collection(string $collection): static
    {
        $this->collection = $collection;
        return $this;
    }

    /**
     * Enable multiple file selection
     */
    public function multiple(bool $multiple = true): static
    {
        $this->multiple = $multiple;
        return $this;
    }

    /**
     * Set upload directory
     */
    public function directory(string $directory): static
    {
        $this->directory = $directory;
        return $this;
    }

    /**
     * Enable/disable picker functionality
     */
    public function enablePicker(bool $enable = true): static
    {
        $this->enablePicker = $enable;
        return $this;
    }

    /**
     * Enable/disable uploader functionality
     */
    public function enableUploader(bool $enable = true): static
    {
        $this->enableUploader = $enable;
        return $this;
    }

    /**
     * Set placeholder text
     */
    public function placeholder(string $placeholder): static
    {
        $this->placeholder = $placeholder;
        return $this;
    }

    /**
     * Set preview size in pixels
     */
    public function previewSize(int $size): static
    {
        $this->previewSize = $size;
        return $this;
    }

    /**
     * Show/hide file name
     */
    public function showFileName(bool $show = true): static
    {
        $this->showFileName = $show;
        return $this;
    }

    /**
     * Show/hide file size
     */
    public function showFileSize(bool $show = true): static
    {
        $this->showFileSize = $show;
        return $this;
    }

    /**
     * Set items per page for picker
     */
    public function perPage(int $perPage): static
    {
        $this->perPage = $perPage;
        return $this;
    }

    /**
     * Filter by current user
     */
    public function byUser(bool $byUser = true): static
    {
        $this->byUser = $byUser;
        return $this;
    }

    /**
     * Filter by specific user ID
     */
    public function byUserId(int $userId): static
    {
        $this->byUserId = $userId;
        return $this;
    }

    /**
     * Enable WebP conversion
     */
    public function convertWebp(bool $convert = true): static
    {
        $this->convertWebp = $convert;
        return $this;
    }

    /**
     * Enable image cropping
     */
    public function enableCrop(bool $enable = true): static
    {
        $this->enableCrop = $enable;
        return $this;
    }

    /**
     * Set crop aspect ratio
     */
    public function cropAspectRatio(int $width, int $height): static
    {
        $this->cropAspectRatio = [$width, $height];
        return $this;
    }

    /**
     * Set crop quality (1-100)
     */
    public function cropQuality(int $quality): static
    {
        $this->cropQuality = max(1, min(100, $quality));
        return $this;
    }

    /**
     * Enable image resizing
     */
    public function enableResize(bool $enable = true): static
    {
        $this->enableResize = $enable;
        return $this;
    }

    /**
     * Set resize dimensions
     */
    public function resizeTo(int $width, ?int $height = null): static
    {
        $this->resizeWidth = $width;
        $this->resizeHeight = $height;
        return $this;
    }

    /**
     * Enable watermark
     */
    public function enableWatermark(bool $enable = true): static
    {
        $this->enableWatermark = $enable;
        return $this;
    }

    /**
     * Set watermark image path
     */
    public function watermarkPath(string $path): static
    {
        $this->watermarkPath = $path;
        return $this;
    }

    /**
     * Set watermark position
     */
    public function watermarkPosition(string $position): static
    {
        $this->watermarkPosition = $position;
        return $this;
    }

    /**
     * Set watermark opacity (0-100)
     */
    public function watermarkOpacity(int $opacity): static
    {
        $this->watermarkOpacity = max(0, min(100, $opacity));
        return $this;
    }

    /**
     * Set placeholder image path
     */
    public function placeholderImage(string $path): static
    {
        $this->placeholderImage = $path;
        return $this;
    }

    // Getter methods untuk view
    public function getAcceptedFileTypes(): array
    {
        return $this->acceptedFileTypes;
    }

    public function getMaxFileSize(): int
    {
        return $this->maxFileSize;
    }

    public function getCollection(): string
    {
        return $this->collection;
    }

    public function isMultiple(): bool
    {
        return $this->multiple;
    }

    public function getDirectory(): ?string
    {
        return $this->directory;
    }

    public function isPickerEnabled(): bool
    {
        return $this->enablePicker;
    }

    public function isUploaderEnabled(): bool
    {
        return $this->enableUploader;
    }

    public function getPlaceholder(): ?string
    {
        return $this->placeholder;
    }

    public function getPreviewSize(): int
    {
        return $this->previewSize;
    }

    public function shouldShowFileName(): bool
    {
        return $this->showFileName;
    }

    public function shouldShowFileSize(): bool
    {
        return $this->showFileSize;
    }

    public function getPerPage(): int
    {
        return $this->perPage;
    }

    public function isByUser(): bool
    {
        return $this->byUser;
    }

    public function getByUserId(): ?int
    {
        return $this->byUserId;
    }

    public function shouldConvertWebp(): bool
    {
        return $this->convertWebp;
    }

    public function isCropEnabled(): bool
    {
        return $this->enableCrop;
    }

    public function getCropAspectRatio(): array
    {
        return $this->cropAspectRatio;
    }

    public function getCropQuality(): int
    {
        return $this->cropQuality;
    }

    public function isResizeEnabled(): bool
    {
        return $this->enableResize;
    }

    public function getResizeWidth(): ?int
    {
        return $this->resizeWidth;
    }

    public function getResizeHeight(): ?int
    {
        return $this->resizeHeight;
    }

    public function isWatermarkEnabled(): bool
    {
        return $this->enableWatermark;
    }

    public function getWatermarkPath(): ?string
    {
        return $this->watermarkPath;
    }

    public function getWatermarkPosition(): string
    {
        return $this->watermarkPosition;
    }

    public function getWatermarkOpacity(): int
    {
        return $this->watermarkOpacity;
    }

    public function getAcceptedTypesString(): string
    {
        return implode(',', $this->acceptedFileTypes);
    }

    public function getPlaceholderImage(): string
    {
        return $this->placeholderImage ?: config('rajagambar.raja_gambar_upload.placeholder_image', '/noimage.jpg');
    }
}
