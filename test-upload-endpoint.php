<?php
// Test upload endpoint dengan cURL
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Fake request untuk bootstrap
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>Test Upload Endpoint dengan cURL</h1>";

// Login user untuk mendapatkan session
$user = \App\Models\User::where('email', '<EMAIL>')->first();
if (!$user) {
    echo "<p>User tidak ditemukan!</p>";
    exit;
}

\Illuminate\Support\Facades\Auth::login($user);
echo "<p>User logged in: " . $user->email . "</p>";

// Generate CSRF token
$token = csrf_token();
echo "<p>CSRF Token: $token</p>";

// Create test PNG file
$testFile = __DIR__ . '/test-upload.png';
$pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
file_put_contents($testFile, $pngData);

echo "<p>Test file created: " . filesize($testFile) . " bytes</p>";

// Test upload dengan internal request
try {
    // Create UploadedFile
    $uploadedFile = new \Illuminate\Http\UploadedFile(
        $testFile,
        'test-upload.png',
        'image/png',
        null,
        true
    );

    // Create request
    $uploadRequest = new \Illuminate\Http\Request();
    $uploadRequest->files->set('file', $uploadedFile);
    $uploadRequest->request->set('collection', 'test');
    $uploadRequest->request->set('convert_webp', '0');
    $uploadRequest->request->set('_token', $token);

    // Set session
    $uploadRequest->setLaravelSession(app('session'));

    echo "<p>Request prepared</p>";

    // Get controller
    $controller = app(\Modules\RajaGambar\app\Http\Controllers\MediaApiController::class);
    
    echo "<p>Controller instantiated</p>";

    // Call upload method
    $response = $controller->upload($uploadRequest);
    
    echo "<h2>Response:</h2>";
    echo "<p>Status: " . $response->getStatusCode() . "</p>";
    echo "<p>Content:</p>";
    echo "<pre>" . $response->getContent() . "</pre>";

} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>Message: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . ":" . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Clean up
if (file_exists($testFile)) {
    unlink($testFile);
    echo "<p>Test file cleaned up</p>";
}

echo "<hr>";
echo "<h2>Test dengan Form HTML</h2>";
?>

<form id="uploadForm" enctype="multipart/form-data">
    <input type="hidden" name="_token" value="<?= $token ?>">
    <input type="file" name="file" accept="image/*" required>
    <input type="hidden" name="collection" value="test">
    <input type="hidden" name="convert_webp" value="0">
    <button type="submit">Upload</button>
</form>

<div id="result"></div>

<script>
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultDiv = document.getElementById('result');
    
    resultDiv.innerHTML = '<p>Uploading...</p>';
    
    fetch('/admin/rajagambar/upload', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': formData.get('_token'),
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text();
    })
    .then(data => {
        console.log('Response data:', data);
        resultDiv.innerHTML = '<h3>Response:</h3><pre>' + data + '</pre>';
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<h3>Error:</h3><p>' + error + '</p>';
    });
});
</script>
