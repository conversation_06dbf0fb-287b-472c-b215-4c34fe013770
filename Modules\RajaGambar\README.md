# RajaGambar Module

Modul Raja<PERSON>bar adalah sistem manajemen media yang terintegrasi dengan FilamentPHP untuk aplikasi hotel. Modul ini menyediakan interface yang user-friendly untuk mengelola file media seperti gambar, video, dan dokumen.

## 🚀 Fitur Utama

### 📸 Manajemen Media
- **Upload Multiple Files**: Upload hingga 20 file sekaligus dengan drag & drop
- **Preview Real-time**: Preview gambar langsung dalam grid layout yang responsif
- **Optimasi Otomatis**: Konversi otomatis ke format WebP untuk performa optimal
- **Image Editor**: Edit gambar dengan berbagai aspect ratio (16:9, 4:3, 1:1)
- **Lazy Loading**: Loading gambar yang efisien untuk performa yang lebih baik

### 🗂️ Organisasi File
- **Koleksi Media**: Organisir file dalam berbagai koleksi (Raja Gambar, Gallery, Dokumen, dll.)
- **Filter Canggih**: Filter berdasarkan tipe file, kole<PERSON><PERSON>, tanggal, dan user
- **Tab Navigation**: Navigasi cepat dengan tab yang sudah dikategorikan
- **Search**: Pencarian file berdasarkan nama dan metadata

### 🎨 Interface Modern
- **Grid Layout Responsif**: Tampilan grid yang menyesuaikan dengan ukuran layar
- **Dark Mode Support**: Dukungan penuh untuk mode gelap
- **Card Design**: Desain card yang modern dengan hover effects
- **Action Buttons**: Tombol aksi yang mudah diakses (View, Download, Edit, Delete)

## 📖 Penggunaan

### Akses Media Manager

1. Login ke admin panel FilamentPHP
2. Navigasi ke menu **"Raja Gambar"** → **"Media Raja Gambar"**
3. Mulai upload dan kelola file media Anda

### Upload File

1. Klik tombol **"Upload Media"** atau gunakan shortcut `Ctrl+U`
2. Pilih file atau drag & drop ke area upload
3. Pilih koleksi yang sesuai
4. Klik **"Upload File"**

## 🔧 Konfigurasi

### Koleksi Media

Koleksi default yang tersedia:
- `rajagambar`: Koleksi utama Raja Gambar
- `gallery`: Galeri umum
- `documents`: Dokumen dan file
- `avatars`: Avatar pengguna
- `banners`: Banner dan header
- `products`: Gambar produk
- `cms`: Konten CMS

## Support

Untuk bantuan dan support, silakan hubungi tim development atau buat issue di repository project.

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
