<?php

return [
    'name' => 'RajaGam<PERSON>',
    'description' => 'Media management module with Spatie Media Library integration for hotel management system',
    'version' => '1.0.0',
    'author' => 'Hotel Management System',

    /*
    |--------------------------------------------------------------------------
    | Module Configuration
    |--------------------------------------------------------------------------
    */
    'enabled' => true,

    /*
    |--------------------------------------------------------------------------
    | Media Configuration
    |--------------------------------------------------------------------------
    */
    'media' => [
        'disk' => 'public',
        'prefix' => 'uploads',
        'max_file_size' => 10240, // 10MB in KB
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
            'application/pdf',
            'video/mp4',
            'video/avi',
            'video/mov',
            'audio/mp3',
            'audio/wav',
        ],
        'collections' => [
            'rajagambar' => 'Raja Gambar',
            'default' => 'Default',
            'gallery' => 'Gallery',
            'documents' => 'Dokumen',
            'avatars' => 'Avatar',
            'banners' => 'Banner',
            'products' => 'Produk',
            'cms' => 'CMS',
        ],
        'conversions' => [
            'thumb' => [
                'width' => 150,
                'height' => 150,
                'format' => 'webp',
                'quality' => 80,
            ],
            'small' => [
                'width' => 300,
                'height' => 300,
                'format' => 'webp',
                'quality' => 85,
            ],
            'medium' => [
                'width' => 600,
                'height' => 600,
                'format' => 'webp',
                'quality' => 90,
            ],
            'large' => [
                'width' => 1200,
                'height' => 1200,
                'format' => 'webp',
                'quality' => 95,
            ],
            'avatar' => [
                'width' => 100,
                'height' => 100,
                'format' => 'webp',
                'quality' => 80,
            ],
            'banner_thumb' => [
                'width' => 400,
                'height' => 200,
                'format' => 'webp',
                'quality' => 85,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | FilamentPHP Configuration
    |--------------------------------------------------------------------------
    */
    'filament' => [
        'enabled' => true,
        'panel' => 'admin',
        'navigation_group' => 'Raja Gambar',
        'navigation_sort' => 1,
        'auto_discovery' => true,
        'resources' => [
            'MediaResource' => [
                'enabled' => true,
                'navigation_label' => 'Media Raja Gambar',
                'navigation_icon' => 'heroicon-o-photo',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Service Configuration
    |--------------------------------------------------------------------------
    */
    'services' => [
        'spatie_media' => [
            'enabled' => true,
            'auto_register' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RajaGambarUpload Component Configuration
    |--------------------------------------------------------------------------
    */
    'raja_gambar_upload' => [
        'default_max_file_size' => 10, // MB
        'default_accepted_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
        ],
        'default_collection' => 'rajagambar',
        'default_preview_size' => 150,
        'default_per_page' => 12,
        'placeholder_image' => '/noimage.jpg', // Path to placeholder/no image file
        'enable_crop' => true,
        'enable_resize' => true,
        'enable_watermark' => true,
        'enable_webp_conversion' => true,
        'show_file_name' => true,
        'show_file_size' => true,
        'enable_picker' => true,
        'enable_uploader' => true,
        'crop' => [
            'default_aspect_ratio' => [16, 9],
            'default_quality' => 90,
            'min_quality' => 1,
            'max_quality' => 100,
        ],
        'watermark' => [
            'default_path' => 'watermark.png',
            'default_position' => 'bottom-right',
            'default_opacity' => 50,
            'min_opacity' => 0,
            'max_opacity' => 100,
            'positions' => [
                'top-left',
                'top-center',
                'top-right',
                'center-left',
                'center',
                'center-right',
                'bottom-left',
                'bottom-center',
                'bottom-right',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto Discovery
    |--------------------------------------------------------------------------
    */
    'auto_discovery' => [
        'enabled' => true,
        'panels' => ['admin'],
        'resources' => true,
        'pages' => true,
        'widgets' => true,
        'components' => true,
    ],
];
