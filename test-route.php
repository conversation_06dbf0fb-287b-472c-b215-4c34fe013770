<?php
// Test route rajagambar
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Fake request untuk bootstrap
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>Test Route RajaGambar</h1>";

// Get all routes
$routes = \Illuminate\Support\Facades\Route::getRoutes();

echo "<h2>All RajaGambar Routes:</h2>";
foreach ($routes as $route) {
    $name = $route->getName();
    $uri = $route->uri();
    
    if (strpos($name, 'rajagambar') !== false || strpos($uri, 'rajagambar') !== false) {
        echo "<p><strong>$name</strong>: $uri</p>";
    }
}

// Test specific route
try {
    $uploadUrl = route('rajagambar.upload');
    echo "<h2>Upload URL: $uploadUrl</h2>";
} catch (Exception $e) {
    echo "<h2>Upload route error: " . $e->getMessage() . "</h2>";
}

// Test controller
try {
    $controller = app(\Modules\RajaGambar\app\Http\Controllers\MediaApiController::class);
    echo "<p>✓ Controller can be instantiated</p>";
} catch (Exception $e) {
    echo "<p>✗ Controller error: " . $e->getMessage() . "</p>";
}
?>
