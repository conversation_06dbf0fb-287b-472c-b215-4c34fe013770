/**
 * Media Grid JavaScript untuk RajaGambar Module
 * Menambahkan interaktivitas dan fitur tambahan untuk media grid
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeMediaGrid();
    initializeImagePreview();
    initializeDragAndDrop();
    initializeKeyboardShortcuts();
    initializeLazyLoading();
});

/**
 * Initialize media grid functionality
 */
function initializeMediaGrid() {
    // Add hover effects untuk media cards
    const mediaCards = document.querySelectorAll('.media-card');
    
    mediaCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-lg', 'scale-105');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-lg', 'scale-105');
        });
    });
    
    // Initialize masonry layout jika diperlukan
    if (window.Masonry) {
        new Masonry('.media-grid', {
            itemSelector: '.media-card',
            columnWidth: 280,
            gutter: 16,
            fitWidth: true
        });
    }
}

/**
 * Initialize image preview modal
 */
function initializeImagePreview() {
    const imageElements = document.querySelectorAll('.media-preview[data-preview="true"]');
    
    imageElements.forEach(img => {
        img.addEventListener('click', function() {
            const src = this.src;
            const alt = this.alt || 'Media Preview';
            
            showImageModal(src, alt);
        });
        
        // Add cursor pointer
        img.style.cursor = 'pointer';
    });
}

/**
 * Show image in modal
 */
function showImageModal(src, alt) {
    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    overlay.style.backdropFilter = 'blur(4px)';
    
    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'relative max-w-4xl max-h-full p-4';
    
    const img = document.createElement('img');
    img.src = src;
    img.alt = alt;
    img.className = 'max-w-full max-h-full object-contain rounded-lg shadow-2xl';
    
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.className = 'absolute top-2 right-2 text-white text-4xl font-bold hover:text-gray-300 transition-colors';
    closeBtn.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
    
    modal.appendChild(img);
    modal.appendChild(closeBtn);
    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    
    // Close modal events
    closeBtn.addEventListener('click', () => document.body.removeChild(overlay));
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            document.body.removeChild(overlay);
        }
    });
    
    // ESC key to close
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            document.body.removeChild(overlay);
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

/**
 * Initialize drag and drop functionality
 */
function initializeDragAndDrop() {
    const uploadArea = document.querySelector('.upload-area');
    if (!uploadArea) return;
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight() {
        uploadArea.classList.remove('dragover');
    }
    
    uploadArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            // Trigger file input change event
            const fileInput = document.querySelector('input[type="file"][multiple]');
            if (fileInput) {
                // Create a new FileList-like object
                const dataTransfer = new DataTransfer();
                Array.from(files).forEach(file => dataTransfer.items.add(file));
                fileInput.files = dataTransfer.files;
                
                // Trigger change event
                fileInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    }
}

/**
 * Initialize keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + U untuk upload
        if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
            e.preventDefault();
            const uploadBtn = document.querySelector('[data-action="upload"]');
            if (uploadBtn) uploadBtn.click();
        }
        
        // F5 untuk refresh
        if (e.key === 'F5') {
            e.preventDefault();
            const refreshBtn = document.querySelector('[data-action="refresh"]');
            if (refreshBtn) refreshBtn.click();
        }
        
        // ESC untuk close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => modal.remove());
        }
    });
}

/**
 * Initialize lazy loading for images
 */
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    const src = img.dataset.src;
                    
                    if (src) {
                        img.src = src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                }
            });
        });
        
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
    }
}

/**
 * Utility functions
 */
const MediaGridUtils = {
    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * Get file type icon
     */
    getFileTypeIcon(mimeType) {
        if (mimeType.startsWith('image/')) return '🖼️';
        if (mimeType.startsWith('video/')) return '🎥';
        if (mimeType.startsWith('audio/')) return '🎵';
        if (mimeType === 'application/pdf') return '📄';
        return '📁';
    },
    
    /**
     * Copy to clipboard
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('Link berhasil disalin!', 'success');
        } catch (err) {
            console.error('Failed to copy: ', err);
            this.showToast('Gagal menyalin link', 'error');
        }
    },
    
    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 notification-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 3000);
    },
    
    /**
     * Refresh media grid
     */
    refreshGrid() {
        window.location.reload();
    }
};

// Export untuk penggunaan global
window.MediaGridUtils = MediaGridUtils;
