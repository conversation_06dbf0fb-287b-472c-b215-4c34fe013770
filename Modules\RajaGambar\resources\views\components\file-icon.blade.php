@props(['mimeType', 'size' => 'w-16 h-16'])

@php
    $iconClass = $size . ' text-gray-400 dark:text-gray-500';
    
    $icon = match(true) {
        str_contains($mimeType, 'pdf') => 'heroicon-o-document-text',
        str_contains($mimeType, 'video') => 'heroicon-o-video-camera',
        str_contains($mimeType, 'audio') => 'heroicon-o-musical-note',
        str_contains($mimeType, 'word') => 'heroicon-o-document',
        str_contains($mimeType, 'excel') => 'heroicon-o-table-cells',
        str_contains($mimeType, 'powerpoint') => 'heroicon-o-presentation-chart-bar',
        str_contains($mimeType, 'zip') || str_contains($mimeType, 'rar') => 'heroicon-o-archive-box',
        default => 'heroicon-o-document'
    };
    
    $bgColor = match(true) {
        str_contains($mimeType, 'pdf') => 'bg-red-100 dark:bg-red-900/20',
        str_contains($mimeType, 'video') => 'bg-purple-100 dark:bg-purple-900/20',
        str_contains($mimeType, 'audio') => 'bg-green-100 dark:bg-green-900/20',
        str_contains($mimeType, 'word') => 'bg-blue-100 dark:bg-blue-900/20',
        str_contains($mimeType, 'excel') => 'bg-emerald-100 dark:bg-emerald-900/20',
        str_contains($mimeType, 'powerpoint') => 'bg-orange-100 dark:bg-orange-900/20',
        str_contains($mimeType, 'zip') || str_contains($mimeType, 'rar') => 'bg-yellow-100 dark:bg-yellow-900/20',
        default => 'bg-gray-100 dark:bg-gray-900/20'
    };
    
    $textColor = match(true) {
        str_contains($mimeType, 'pdf') => 'text-red-600 dark:text-red-400',
        str_contains($mimeType, 'video') => 'text-purple-600 dark:text-purple-400',
        str_contains($mimeType, 'audio') => 'text-green-600 dark:text-green-400',
        str_contains($mimeType, 'word') => 'text-blue-600 dark:text-blue-400',
        str_contains($mimeType, 'excel') => 'text-emerald-600 dark:text-emerald-400',
        str_contains($mimeType, 'powerpoint') => 'text-orange-600 dark:text-orange-400',
        str_contains($mimeType, 'zip') || str_contains($mimeType, 'rar') => 'text-yellow-600 dark:text-yellow-400',
        default => 'text-gray-600 dark:text-gray-400'
    };
@endphp

<div class="w-full h-48 {{ $bgColor }} flex items-center justify-center rounded-t-lg">
    <div class="text-center">
        <x-dynamic-component :component="$icon" class="{{ $size }} {{ $textColor }} mx-auto mb-2" />
        <span class="text-xs {{ $textColor }} font-medium uppercase tracking-wide">
            {{ strtoupper(pathinfo($mimeType, PATHINFO_EXTENSION)) }}
        </span>
    </div>
</div>
