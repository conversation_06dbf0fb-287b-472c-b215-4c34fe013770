<?php

namespace Modules\RajaGambar\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use <PERSON>widart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Modules\RajaGambar\Services\SpatieMediaService;
use Modules\RajaGambar\Models\Media;
use Modules\RajaGambar\Observers\MediaObserver;

class RajaGambarServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'RajaGambar';

    protected string $nameLower = 'rajagambar';
    protected string $panel = 'admin';
 
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->name, 'database/migrations'));

        // Register Media Observer untuk set user_id otomatis
        if (class_exists(MediaObserver::class)) {
            Media::observe(MediaObserver::class);
        }

        // Register assets
        $this->registerAssets();
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);

        // Register SpatieMediaService
        $this->app->singleton(SpatieMediaService::class, function () {
            return new SpatieMediaService();
        });

        // Register service alias
        $this->app->alias(SpatieMediaService::class, 'rajagambar.media');
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        // $this->commands([]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/'.$this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $relativeConfigPath = config('modules.paths.generator.config.path');
        $configPath = module_path($this->name, $relativeConfigPath);

        if (is_dir($configPath)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($configPath));

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $relativePath = str_replace($configPath . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $configKey = $this->nameLower . '.' . str_replace([DIRECTORY_SEPARATOR, '.php'], ['.', ''], $relativePath);
                    $key = ($relativePath === 'config.php') ? $this->nameLower : $configKey;

                    $this->publishes([$file->getPathname() => config_path($relativePath)], 'config');
                    $this->mergeConfigFrom($file->getPathname(), $key);
                }
            }
        }
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/'.$this->nameLower);
        $sourcePath = module_path($this->name, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower.'-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->nameLower);

        $componentNamespace = $this->module_namespace($this->name, $this->app_path(config('modules.paths.generator.component-class.path')));
        Blade::componentNamespace($componentNamespace, $this->nameLower);
    }

    /**
     * Register assets (CSS & JS).
     */
    protected function registerAssets(): void
    {
        // Publish CSS assets
        $this->publishes([
            module_path($this->name, 'resources/css') => public_path('modules/rajagambar/css'),
        ], 'rajagambar-assets');

        // Publish JS assets
        $this->publishes([
            module_path($this->name, 'resources/js') => public_path('modules/rajagambar/js'),
        ], 'rajagambar-assets');

        // Auto-publish assets in development
        if (app()->environment('local')) {
            $this->publishes([
                module_path($this->name, 'resources/css') => public_path('modules/rajagambar/css'),
                module_path($this->name, 'resources/js') => public_path('modules/rajagambar/js'),
            ], 'rajagambar-assets');
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            SpatieMediaService::class,
            'rajagambar.media',
        ];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path.'/modules/'.$this->nameLower)) {
                $paths[] = $path.'/modules/'.$this->nameLower;
            }
        }

        return $paths;
    }
}
