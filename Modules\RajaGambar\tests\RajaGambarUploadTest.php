<?php

namespace Modules\RajaGambar\Tests;

use Tests\TestCase;
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUpload;
use Filament\Forms\Components\Field;

class RajaGambarUploadTest extends TestCase
{
    /**
     * Test basic component instantiation
     */
    public function test_component_can_be_instantiated()
    {
        $component = RajaGambarUpload::make('test_field');
        
        $this->assertInstanceOf(Field::class, $component);
        $this->assertInstanceOf(RajaGambarUpload::class, $component);
    }

    /**
     * Test default configuration
     */
    public function test_default_configuration()
    {
        $component = RajaGambarUpload::make('test_field');
        
        $this->assertEquals(['image/jpeg', 'image/png', 'image/gif', 'image/webp'], $component->getAcceptedFileTypes());
        $this->assertEquals(10, $component->getMaxFileSize());
        $this->assertEquals('rajagambar', $component->getCollection());
        $this->assertFalse($component->isMultiple());
        $this->assertTrue($component->isPickerEnabled());
        $this->assertTrue($component->isUploaderEnabled());
        $this->assertEquals(150, $component->getPreviewSize());
    }

    /**
     * Test method chaining configuration
     */
    public function test_method_chaining_configuration()
    {
        $component = RajaGambarUpload::make('test_field')
            ->acceptedFileTypes(['image/jpeg', 'image/png'])
            ->maxFileSize(5)
            ->collection('custom')
            ->multiple(true)
            ->directory('uploads/custom')
            ->enablePicker(false)
            ->enableUploader(true)
            ->placeholder('Custom placeholder')
            ->previewSize(200)
            ->showFileName(false)
            ->showFileSize(false)
            ->perPage(20)
            ->byUser(true)
            ->convertWebp(true);

        $this->assertEquals(['image/jpeg', 'image/png'], $component->getAcceptedFileTypes());
        $this->assertEquals(5, $component->getMaxFileSize());
        $this->assertEquals('custom', $component->getCollection());
        $this->assertTrue($component->isMultiple());
        $this->assertEquals('uploads/custom', $component->getDirectory());
        $this->assertFalse($component->isPickerEnabled());
        $this->assertTrue($component->isUploaderEnabled());
        $this->assertEquals('Custom placeholder', $component->getPlaceholder());
        $this->assertEquals(200, $component->getPreviewSize());
        $this->assertFalse($component->shouldShowFileName());
        $this->assertFalse($component->shouldShowFileSize());
        $this->assertEquals(20, $component->getPerPage());
        $this->assertTrue($component->isByUser());
        $this->assertTrue($component->shouldConvertWebp());
    }

    /**
     * Test image processing configuration
     */
    public function test_image_processing_configuration()
    {
        $component = RajaGambarUpload::make('test_field')
            ->enableCrop(true)
            ->cropAspectRatio(16, 9)
            ->cropQuality(85)
            ->enableResize(true)
            ->resizeTo(1200, 800)
            ->enableWatermark(true)
            ->watermarkPath('watermark.png')
            ->watermarkPosition('bottom-left')
            ->watermarkOpacity(75);

        $this->assertTrue($component->isCropEnabled());
        $this->assertEquals([16, 9], $component->getCropAspectRatio());
        $this->assertEquals(85, $component->getCropQuality());
        $this->assertTrue($component->isResizeEnabled());
        $this->assertEquals(1200, $component->getResizeWidth());
        $this->assertEquals(800, $component->getResizeHeight());
        $this->assertTrue($component->isWatermarkEnabled());
        $this->assertEquals('watermark.png', $component->getWatermarkPath());
        $this->assertEquals('bottom-left', $component->getWatermarkPosition());
        $this->assertEquals(75, $component->getWatermarkOpacity());
    }

    /**
     * Test accepted types string generation
     */
    public function test_accepted_types_string()
    {
        $component = RajaGambarUpload::make('test_field')
            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->assertEquals('image/jpeg,image/png,image/webp', $component->getAcceptedTypesString());
    }

    /**
     * Test quality bounds
     */
    public function test_quality_bounds()
    {
        $component = RajaGambarUpload::make('test_field');

        // Test crop quality bounds
        $component->cropQuality(150); // Above max
        $this->assertEquals(100, $component->getCropQuality());

        $component->cropQuality(-10); // Below min
        $this->assertEquals(1, $component->getCropQuality());

        $component->cropQuality(75); // Normal value
        $this->assertEquals(75, $component->getCropQuality());

        // Test watermark opacity bounds
        $component->watermarkOpacity(150); // Above max
        $this->assertEquals(100, $component->getWatermarkOpacity());

        $component->watermarkOpacity(-10); // Below min
        $this->assertEquals(0, $component->getWatermarkOpacity());

        $component->watermarkOpacity(50); // Normal value
        $this->assertEquals(50, $component->getWatermarkOpacity());
    }

    /**
     * Test view path
     */
    public function test_view_path()
    {
        $component = RajaGambarUpload::make('test_field');
        
        // Use reflection to access protected property
        $reflection = new \ReflectionClass($component);
        $viewProperty = $reflection->getProperty('view');
        $viewProperty->setAccessible(true);
        
        $this->assertEquals('rajagambar::components.forms.raja-gambar-upload', $viewProperty->getValue($component));
    }

    /**
     * Test user filtering configuration
     */
    public function test_user_filtering()
    {
        $component = RajaGambarUpload::make('test_field');

        // Test byUser
        $component->byUser(true);
        $this->assertTrue($component->isByUser());
        $this->assertNull($component->getByUserId());

        // Test byUserId
        $component->byUserId(123);
        $this->assertEquals(123, $component->getByUserId());

        // Test both
        $component->byUser(false)->byUserId(456);
        $this->assertFalse($component->isByUser());
        $this->assertEquals(456, $component->getByUserId());
    }

    /**
     * Test resize configuration
     */
    public function test_resize_configuration()
    {
        $component = RajaGambarUpload::make('test_field');

        // Test resize with both dimensions
        $component->resizeTo(800, 600);
        $this->assertEquals(800, $component->getResizeWidth());
        $this->assertEquals(600, $component->getResizeHeight());

        // Test resize with width only
        $component->resizeTo(1200);
        $this->assertEquals(1200, $component->getResizeWidth());
        $this->assertNull($component->getResizeHeight());
    }

    /**
     * Test watermark positions
     */
    public function test_watermark_positions()
    {
        $component = RajaGambarUpload::make('test_field');

        $positions = [
            'top-left',
            'top-center', 
            'top-right',
            'center-left',
            'center',
            'center-right',
            'bottom-left',
            'bottom-center',
            'bottom-right'
        ];

        foreach ($positions as $position) {
            $component->watermarkPosition($position);
            $this->assertEquals($position, $component->getWatermarkPosition());
        }
    }
}
