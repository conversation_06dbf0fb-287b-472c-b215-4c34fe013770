<?php
// Test upload debug untuk RajaGambar
// Akses: https://hotel.rid/test-upload-debug.php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Fake request untuk bootstrap
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>RajaGambar Upload Debug</h1>";

// Test 1: Check config
echo "<h2>1. Config Test</h2>";
echo "<pre>";
echo "Max file size: " . config('rajagambar.media.max_file_size', 'NOT SET') . " KB\n";
echo "Allowed mime types: " . json_encode(config('rajagambar.media.allowed_mime_types', []), JSON_PRETTY_PRINT) . "\n";
echo "Upload config path: " . config('rajagambar.upload.max_file_size', 'NOT SET') . "\n";
echo "</pre>";

// Test 2: Check model
echo "<h2>2. Model Test</h2>";
echo "<pre>";
try {
    $model = new \Modules\RajaGambar\Models\RajaGaleri();
    echo "RajaGaleri model: OK\n";
    echo "HasMedia trait: " . (in_array('Spatie\MediaLibrary\InteractsWithMedia', class_uses($model)) ? 'OK' : 'MISSING') . "\n";
    
    // Test create model
    $testModel = \Modules\RajaGambar\Models\RajaGaleri::create([
        'user_id' => null,
        'name' => 'Test Gallery',
        'description' => 'Test gallery for debug',
    ]);
    echo "Model creation: OK (ID: " . $testModel->id . ")\n";
    
    // Clean up
    $testModel->delete();
    echo "Model cleanup: OK\n";
    
} catch (\Exception $e) {
    echo "Model error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
echo "</pre>";

// Test 3: Check database
echo "<h2>3. Database Test</h2>";
echo "<pre>";
try {
    $mediaCount = \Spatie\MediaLibrary\MediaCollections\Models\Media::count();
    echo "Media table accessible: OK (count: $mediaCount)\n";
    
    $galeriCount = \Modules\RajaGambar\Models\RajaGaleri::count();
    echo "RajaGaleri table accessible: OK (count: $galeriCount)\n";
    
} catch (\Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
echo "</pre>";

// Test 4: Check routes
echo "<h2>4. Routes Test</h2>";
echo "<pre>";
try {
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $uploadRoute = null;
    
    foreach ($routes as $route) {
        if ($route->uri() === 'admin/rajagambar/upload') {
            $uploadRoute = $route;
            break;
        }
    }
    
    if ($uploadRoute) {
        echo "Upload route found: OK\n";
        echo "URI: " . $uploadRoute->uri() . "\n";
        echo "Methods: " . implode(', ', $uploadRoute->methods()) . "\n";
        echo "Action: " . $uploadRoute->getActionName() . "\n";
    } else {
        echo "Upload route: NOT FOUND\n";
    }
    
} catch (\Exception $e) {
    echo "Routes error: " . $e->getMessage() . "\n";
}
echo "</pre>";

// Test 5: Check storage
echo "<h2>5. Storage Test</h2>";
echo "<pre>";
try {
    $disk = \Illuminate\Support\Facades\Storage::disk('public');
    echo "Public disk accessible: " . ($disk ? 'OK' : 'FAILED') . "\n";
    
    $path = storage_path('app/public');
    echo "Storage path: $path\n";
    echo "Storage writable: " . (is_writable($path) ? 'OK' : 'FAILED') . "\n";
    
    // Test write
    $testFile = 'test-' . time() . '.txt';
    $disk->put($testFile, 'test content');
    echo "Test write: " . ($disk->exists($testFile) ? 'OK' : 'FAILED') . "\n";
    
    // Clean up
    $disk->delete($testFile);
    echo "Test cleanup: OK\n";
    
} catch (\Exception $e) {
    echo "Storage error: " . $e->getMessage() . "\n";
}
echo "</pre>";

// Test 6: Validation test
echo "<h2>6. Validation Test</h2>";
echo "<pre>";
try {
    $maxFileSize = config('rajagambar.media.max_file_size', 10240);
    
    $rules = [
        'file' => [
            'required',
            'file',
            'image',
            'max:' . $maxFileSize,
            'mimes:jpeg,jpg,png,gif,webp,svg'
        ],
        'collection' => 'nullable|string|max:255',
        'convert_webp' => 'nullable|boolean',
    ];
    
    echo "Validation rules:\n";
    echo json_encode($rules, JSON_PRETTY_PRINT) . "\n";
    echo "Max file size rule: max:$maxFileSize (KB)\n";
    
} catch (\Exception $e) {
    echo "Validation error: " . $e->getMessage() . "\n";
}
echo "</pre>";

echo "<h2>7. Upload Form Test</h2>";
?>
<form action="/admin/rajagambar/upload" method="POST" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <p>
        <label>File:</label><br>
        <input type="file" name="file" accept="image/*" required>
    </p>
    <p>
        <label>Collection:</label><br>
        <input type="text" name="collection" value="test" placeholder="test">
    </p>
    <p>
        <label>Convert WebP:</label><br>
        <input type="checkbox" name="convert_webp" value="1">
    </p>
    <p>
        <button type="submit">Test Upload</button>
    </p>
</form>

<script>
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/rajagambar/upload', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
            'Accept': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response:', data);
        alert('Response: ' + JSON.stringify(data, null, 2));
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error.message);
    });
});
</script>

<?php
echo "<p><strong>Debug completed.</strong></p>";
echo "<p>Jika semua test OK, coba upload file menggunakan form di atas.</p>";
echo "<p>Buka Developer Console (F12) untuk melihat response detail.</p>";
?>
