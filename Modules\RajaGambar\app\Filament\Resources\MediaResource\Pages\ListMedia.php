<?php

namespace Modules\RajaGambar\Filament\Resources\MediaResource\Pages;

use Mo<PERSON>les\RajaGambar\Models\RajaGaleri;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Modules\RajaGambar\Filament\Resources\MediaResource;
use Modules\RajaGambar\Models\Media;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListMedia extends ListRecords
{
    protected static string $resource = MediaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('upload')
                ->label('Upload Media')
                ->icon('heroicon-o-cloud-arrow-up')
                ->color('primary')
                ->size('lg')
                ->form([
                    Section::make('Upload File')
                        ->description('Drag & drop file atau klik untuk memilih file yang ingin diupload')
                        ->icon('heroicon-o-photo')
                        ->schema([
                            FileUpload::make('files')
                                ->label('File Media')
                                ->multiple()
                                ->acceptedFileTypes([
                                    'image/jpeg',
                                    'image/png',
                                    'image/gif',
                                    'image/webp',
                                    'image/svg+xml',
                                    'application/pdf',
                                    'video/mp4',
                                    'video/avi',
                                    'video/mov',
                                    'audio/mp3',
                                    'audio/wav',
                                ])
                                ->maxSize(10240) // 10MB
                                ->maxFiles(20)
                                ->previewable()
                                ->reorderable()
                                ->required()
                                ->helperText('Maksimal 20 file, ukuran per file maksimal 10MB. Format yang didukung: JPG, PNG, GIF, WebP, SVG, PDF, MP4, AVI, MOV, MP3, WAV')
                                ->directory('media-uploads')
                                ->visibility('public')
                                ->imageEditor()
                                ->imageEditorAspectRatios([
                                    null,
                                    '16:9',
                                    '4:3',
                                    '1:1',
                                ])
                                ->optimize('webp')
                                ->resize(1920, 1080),

                            Select::make('collection_name')
                                ->label('Koleksi Media')
                                ->options([
                                    'rajagambar' => '📸 Raja Gambar',
                                    'default' => '📁 Default',
                                    'gallery' => '🖼️ Gallery',
                                    'documents' => '📄 Dokumen',
                                    'avatars' => '👤 Avatar',
                                    'banners' => '🎯 Banner',
                                    'products' => '🛍️ Produk',
                                    'cms' => '📝 CMS',
                                ])
                                ->default('rajagambar')
                                ->required()
                                ->searchable()
                                ->helperText('Pilih koleksi untuk mengorganisir dan mengelompokkan file media'),
                        ])
                        ->columns(1)
                        ->collapsible(),
                ])
                ->modalWidth('2xl')
                ->modalSubmitActionLabel('Upload File')
                ->modalCancelActionLabel('Batal')
                ->action(function (array $data) {
                    $this->processUpload($data);
                })
                ->successNotificationTitle('Media berhasil diupload!')
                ->keyBindings(['mod+u']),

            Action::make('refresh')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(fn () => $this->refreshFormData())
                ->keyBindings(['f5']),

            Actions\CreateAction::make()
                ->label('Tambah Manual')
                ->icon('heroicon-o-plus')
                ->color('success'),
        ];
    }

    protected function processUpload(array $data): void
    {
        $uploadedFiles = $data['files'] ?? [];
        $collectionName = $data['collection_name'] ?? 'rajagambar';

        if (empty($uploadedFiles)) {
            Notification::make()
                ->title('❌ Error Upload')
                ->body('Tidak ada file yang dipilih untuk diupload')
                ->danger()
                ->duration(5000)
                ->send();
            return;
        }

        // Notification untuk memulai proses
        Notification::make()
            ->title('🔄 Memproses Upload')
            ->body('Sedang mengupload ' . count($uploadedFiles) . ' file...')
            ->info()
            ->duration(3000)
            ->send();

        $successCount = 0;
        $errorCount = 0;
        $successFiles = [];
        $errorFiles = [];

        foreach ($uploadedFiles as $uploadedFile) {
            try {
                // Buat record RajaGaleri baru untuk setiap file
                $mediaRecord = new RajaGaleri();
                $mediaRecord->save();

                // Coba menggunakan Storage facade untuk mendapatkan path yang benar
                $tempPath = null;
                $originalName = $uploadedFile;

                $mlprefix = config('media-library.prefix', 'uploads');
                // Coba berbagai disk dan path
                $diskPaths = [
                    ['disk' => 'public', 'path' => $mlprefix . '/' . $uploadedFile],
                    ['disk' => 'public', 'path' => $uploadedFile],
                    ['disk' => 'local', 'path' => 'livewire-tmp/' . $uploadedFile],
                    ['disk' => 'local', 'path' => $uploadedFile],
                ];

                foreach ($diskPaths as $diskPath) {
                    if (Storage::disk($diskPath['disk'])->exists($diskPath['path'])) {
                        $tempPath = Storage::disk($diskPath['disk'])->path($diskPath['path']);
                        break;
                    }
                }

                if (!$tempPath || !file_exists($tempPath)) {
                    Log::warning('File not found for upload', ['file' => $uploadedFile]);
                    $errorCount++;
                    $errorFiles[] = basename($uploadedFile);
                    continue;
                }

                $originalName = basename($uploadedFile);
                $fileName = pathinfo($uploadedFile, PATHINFO_FILENAME);

                // Tambahkan file ke media collection
                $media = $mediaRecord
                    ->addMedia($tempPath)
                    ->usingName($fileName)
                    ->usingFileName($originalName)
                    ->toMediaCollection($collectionName);

                if ($media) {
                    $successCount++;
                    $successFiles[] = $media->name;

                    $mediaRecord->update([
                        'record_id' => $media->id,
                        'nama' => $media->name,
                        'key' => $media->uuid,
                        'value' => $media->getUrl(),
                    ]);
                } else {
                    $errorCount++;
                    $errorFiles[] = basename($uploadedFile);
                }

            } catch (\Exception $e) {
                Log::error('Error processing upload', [
                    'file' => $uploadedFile,
                    'error' => $e->getMessage()
                ]);
                $errorCount++;
                $errorFiles[] = basename($uploadedFile);
            }
        }

        // Tampilkan notifikasi hasil yang lebih detail
        if ($successCount > 0 && $errorCount === 0) {
            Notification::make()
                ->title('✅ Upload Berhasil!')
                ->body("Semua $successCount file berhasil diupload ke koleksi '$collectionName'")
                ->success()
                ->duration(8000)
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view')
                        ->button()
                        ->url(static::getUrl())
                        ->label('Lihat Media'),
                ])
                ->send();
        } elseif ($successCount > 0 && $errorCount > 0) {
            Notification::make()
                ->title('⚠️ Upload Sebagian Berhasil')
                ->body("$successCount file berhasil, $errorCount file gagal diupload")
                ->warning()
                ->duration(10000)
                ->send();
        } else {
            Notification::make()
                ->title('❌ Upload Gagal')
                ->body('Semua file gagal diupload. Periksa format dan ukuran file.')
                ->danger()
                ->duration(10000)
                ->send();
        }

        // Refresh halaman untuk menampilkan file baru
        $this->dispatch('$refresh');
    }

    public function getTabs(): array
    {
        $totalCount = Media::count();
        $rajagambarCount = Media::where('collection_name', 'rajagambar')->count();
        $imageCount = Media::where('mime_type', 'LIKE', 'image/%')->count();
        $documentCount = Media::whereIn('mime_type', [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ])->count();
        $videoCount = Media::where('mime_type', 'LIKE', 'video/%')->count();
        $recentCount = Media::where('created_at', '>=', now()->subDays(7))->count();

        return [
            'all' => Tab::make('📁 Semua Media')
                ->badge($totalCount)
                ->badgeColor('gray'),

            'rajagambar' => Tab::make('📸 Raja Gambar')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('collection_name', 'rajagambar'))
                ->badge($rajagambarCount)
                ->badgeColor('success'),

            'images' => Tab::make('🖼️ Gambar')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('mime_type', 'LIKE', 'image/%'))
                ->badge($imageCount)
                ->badgeColor('info'),

            'documents' => Tab::make('📄 Dokumen')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('mime_type', [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                ]))
                ->badge($documentCount)
                ->badgeColor('warning'),

            'videos' => Tab::make('🎥 Video')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('mime_type', 'LIKE', 'video/%'))
                ->badge($videoCount)
                ->badgeColor('danger'),

            'recent' => Tab::make('🕒 Terbaru (7 hari)')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('created_at', '>=', now()->subDays(7)))
                ->badge($recentCount)
                ->badgeColor('primary'),
        ];
    }
}
