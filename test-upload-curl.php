<?php
// Test upload dengan cURL
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Fake request untuk bootstrap
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>Test Upload dengan cURL</h1>";

// Simulate login untuk mendapatkan session
$user = \App\Models\User::where('email', '<EMAIL>')->first();
if ($user) {
    \Illuminate\Support\Facades\Auth::login($user);
    echo "<p>User logged in: " . $user->email . "</p>";
} else {
    echo "<p>User not found!</p>";
    exit;
}

// Generate CSRF token
$token = csrf_token();
echo "<p>CSRF Token: $token</p>";

// Test upload
$uploadUrl = url('/admin/rajagambar/upload');
echo "<p>Upload URL: $uploadUrl</p>";

// Prepare file
$filePath = __DIR__ . '/test-upload.png';
if (!file_exists($filePath)) {
    echo "<p>File not found: $filePath</p>";
    exit;
}

echo "<p>File exists: " . filesize($filePath) . " bytes</p>";

// Test dengan file_get_contents dan stream context
$postData = [
    'collection' => 'test',
    'convert_webp' => '0',
    '_token' => $token
];

// Create multipart form data
$boundary = '----WebKitFormBoundary' . uniqid();
$data = '';

// Add regular fields
foreach ($postData as $key => $value) {
    $data .= "--$boundary\r\n";
    $data .= "Content-Disposition: form-data; name=\"$key\"\r\n\r\n";
    $data .= "$value\r\n";
}

// Add file
$data .= "--$boundary\r\n";
$data .= "Content-Disposition: form-data; name=\"file\"; filename=\"test-upload.png\"\r\n";
$data .= "Content-Type: image/png\r\n\r\n";
$data .= file_get_contents($filePath) . "\r\n";
$data .= "--$boundary--\r\n";

// Create context
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            "Content-Type: multipart/form-data; boundary=$boundary",
            "Content-Length: " . strlen($data),
            "X-CSRF-TOKEN: $token",
            "Accept: application/json",
        ],
        'content' => $data
    ]
]);

echo "<h2>Sending request...</h2>";

// Send request
$result = file_get_contents($uploadUrl, false, $context);

if ($result === false) {
    echo "<p>Request failed!</p>";
    echo "<pre>";
    print_r(error_get_last());
    echo "</pre>";
} else {
    echo "<h2>Response:</h2>";
    echo "<pre>";
    echo htmlspecialchars($result);
    echo "</pre>";
    
    // Try to decode JSON
    $json = json_decode($result, true);
    if ($json) {
        echo "<h2>Parsed JSON:</h2>";
        echo "<pre>";
        print_r($json);
        echo "</pre>";
    }
}

// Check HTTP response headers
if (isset($http_response_header)) {
    echo "<h2>Response Headers:</h2>";
    echo "<pre>";
    foreach ($http_response_header as $header) {
        echo htmlspecialchars($header) . "\n";
    }
    echo "</pre>";
}
?>
