# RajaGambarUpload Component

Custom FilamentPHP field component untuk upload dan pemilihan gambar dengan integrasi ke sistem RajaGambar.

## Fitur Utama

- **Upload Drag & Drop**: Upload file dengan drag and drop atau klik untuk memilih
- **Media Picker**: Pilih dari galeri media yang sudah ada
- **Multiple Selection**: Mendukung pemilihan single atau multiple file
- **Preview**: Preview gambar dengan thumbnail
- **Collection Management**: Organisasi file berdasarkan collection
- **User Filtering**: Filter media berdasarkan user
- **WebP Conversion**: Konversi otomatis ke format WebP
- **Image Processing**: Crop, resize, dan watermark
- **Responsive Design**: UI yang responsif dan user-friendly

## Instalasi

Component sudah terdaftar otomatis melalui service provider RajaGambar. Pastikan modul RajaGambar sudah aktif.

## Penggunaan Dasar

```php
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUpload;

// Single image upload
RajaGambarUpload::make('featured_image')
    ->label('Gambar Utama')
    ->collection('featured')
    ->maxFileSize(5) // 5MB
    ->previewSize(200)

// Multiple images upload
RajaGambarUpload::make('gallery_images')
    ->label('Galeri Foto')
    ->multiple()
    ->collection('gallery')
    ->maxFileSize(10)
    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
```

## Konfigurasi Lengkap

```php
RajaGambarUpload::make('images')
    ->label('Upload Gambar')
    ->placeholder('Pilih atau upload gambar Anda')
    
    // File Configuration
    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
    ->maxFileSize(10) // MB
    ->multiple(true)
    
    // Collection & Organization
    ->collection('rajagambar')
    ->directory('uploads/custom')
    
    // UI Configuration
    ->previewSize(150) // pixels
    ->showFileName(true)
    ->showFileSize(true)
    ->perPage(12) // items per page in picker
    
    // User Filtering
    ->byUser(true) // filter by current user
    ->byUserId(123) // filter by specific user ID
    
    // Features
    ->enablePicker(true) // enable media picker
    ->enableUploader(true) // enable file uploader
    
    // Image Processing
    ->convertWebp(true)
    ->enableCrop(true)
    ->cropAspectRatio(16, 9)
    ->cropQuality(90)
    ->enableResize(true)
    ->resizeTo(1200, 800)
    ->enableWatermark(true)
    ->watermarkPath('watermark.png')
    ->watermarkPosition('bottom-right')
    ->watermarkOpacity(50)
```

## Method Reference

### File Configuration

- `acceptedFileTypes(array $types)` - Set tipe file yang diterima
- `maxFileSize(int $size)` - Set ukuran maksimal file dalam MB
- `multiple(bool $multiple = true)` - Enable multiple file selection

### Collection & Organization

- `collection(string $collection)` - Set nama collection
- `directory(string $directory)` - Set direktori upload

### UI Configuration

- `placeholder(string $placeholder)` - Set placeholder text
- `previewSize(int $size)` - Set ukuran preview dalam pixels
- `showFileName(bool $show = true)` - Show/hide nama file
- `showFileSize(bool $show = true)` - Show/hide ukuran file
- `perPage(int $perPage)` - Set jumlah item per halaman di picker

### User Filtering

- `byUser(bool $byUser = true)` - Filter berdasarkan user saat ini
- `byUserId(int $userId)` - Filter berdasarkan user ID tertentu

### Features

- `enablePicker(bool $enable = true)` - Enable/disable media picker
- `enableUploader(bool $enable = true)` - Enable/disable file uploader

### Image Processing

- `convertWebp(bool $convert = true)` - Enable konversi WebP
- `enableCrop(bool $enable = true)` - Enable image cropping
- `cropAspectRatio(int $width, int $height)` - Set aspect ratio crop
- `cropQuality(int $quality)` - Set kualitas crop (1-100)
- `enableResize(bool $enable = true)` - Enable image resizing
- `resizeTo(int $width, int $height = null)` - Set dimensi resize
- `enableWatermark(bool $enable = true)` - Enable watermark
- `watermarkPath(string $path)` - Set path watermark image
- `watermarkPosition(string $position)` - Set posisi watermark
- `watermarkOpacity(int $opacity)` - Set opacity watermark (0-100)

## Contoh Penggunaan dalam Form

```php
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUpload;

public static function form(Form $form): Form
{
    return $form
        ->schema([
            Section::make('Media')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            RajaGambarUpload::make('featured_image')
                                ->label('Gambar Utama')
                                ->collection('featured')
                                ->maxFileSize(5)
                                ->previewSize(200)
                                ->required(),
                                
                            RajaGambarUpload::make('thumbnail')
                                ->label('Thumbnail')
                                ->collection('thumbnails')
                                ->maxFileSize(2)
                                ->previewSize(150)
                                ->enableCrop(true)
                                ->cropAspectRatio(1, 1),
                        ]),
                        
                    RajaGambarUpload::make('gallery')
                        ->label('Galeri Foto')
                        ->multiple()
                        ->collection('gallery')
                        ->maxFileSize(10)
                        ->perPage(16)
                        ->convertWebp(true),
                ])
        ]);
}
```

## API Endpoints

Component menggunakan API endpoints berikut:

- `GET /admin/rajagambar/media-api` - Get media list untuk picker
- `POST /admin/rajagambar/upload` - Upload file baru
- `GET /admin/rajagambar/media/{id}/preview` - Get preview image
- `GET /admin/rajagambar/media/{id}` - Get media details
- `DELETE /admin/rajagambar/media/{id}` - Delete media
- `POST /admin/rajagambar/media/bulk` - Bulk operations

## Styling

Component menggunakan CSS yang sudah terintegrasi dengan Tailwind CSS dan FilamentPHP theme. Styling dapat dikustomisasi melalui CSS custom atau dengan override class.

## Events

Component mengirim events berikut:

- `upload-success` - Ketika upload berhasil
- `upload-error` - Ketika upload gagal
- `media-selected` - Ketika media dipilih dari picker
- `media-removed` - Ketika media dihapus

## Troubleshooting

### Upload Gagal
- Periksa ukuran file tidak melebihi `maxFileSize`
- Pastikan tipe file sesuai dengan `acceptedFileTypes`
- Periksa permission direktori upload

### Media Tidak Muncul di Picker
- Pastikan collection name sesuai
- Periksa filter user jika menggunakan `byUser()`
- Pastikan media sudah tersimpan di database

### Preview Tidak Muncul
- Pastikan file masih ada di storage
- Periksa URL media dapat diakses
- Pastikan conversion preview sudah dibuat

## Lisensi

Component ini adalah bagian dari modul RajaGambar dan mengikuti lisensi yang sama dengan project utama.
