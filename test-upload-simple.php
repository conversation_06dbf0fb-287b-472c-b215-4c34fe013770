<?php
// Simple test untuk upload endpoint
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Fake request untuk bootstrap
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>Test Upload Endpoint</h1>";

// Test 1: Check route exists
try {
    $url = route('rajagambar.upload');
    echo "<p>✓ Route exists: $url</p>";
} catch (Exception $e) {
    echo "<p>✗ Route error: " . $e->getMessage() . "</p>";
}

// Test 2: Check controller exists
try {
    $controller = new \Modules\RajaGambar\app\Http\Controllers\MediaApiController();
    echo "<p>✓ Controller exists</p>";
} catch (Exception $e) {
    echo "<p>✗ Controller error: " . $e->getMessage() . "</p>";
}

// Test 3: Check config
$config = config('rajagambar.media');
echo "<p>Config:</p>";
echo "<pre>" . print_r($config, true) . "</pre>";

// Test 4: Check validation rules
$maxFileSize = config('rajagambar.media.max_file_size', 10240);
echo "<p>Max file size: {$maxFileSize}KB</p>";

// Test 5: Check if we can create a test file
$testFile = __DIR__ . '/test-small.png';
$pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==');
file_put_contents($testFile, $pngData);

if (file_exists($testFile)) {
    echo "<p>✓ Test file created: " . filesize($testFile) . " bytes</p>";
} else {
    echo "<p>✗ Failed to create test file</p>";
}

// Test 6: Check MIME type
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $testFile);
finfo_close($finfo);
echo "<p>MIME type: $mimeType</p>";

// Test 7: Test validation manually
$validator = \Illuminate\Support\Facades\Validator::make([
    'file' => new \Illuminate\Http\UploadedFile($testFile, 'test.png', $mimeType, null, true)
], [
    'file' => [
        'required',
        'file',
        'image',
        'max:' . $maxFileSize,
        'mimes:jpeg,jpg,png,gif,webp,svg'
    ]
]);

if ($validator->passes()) {
    echo "<p>✓ Validation passes</p>";
} else {
    echo "<p>✗ Validation fails:</p>";
    echo "<pre>" . print_r($validator->errors()->toArray(), true) . "</pre>";
}

// Clean up
unlink($testFile);
?>

<form action="/admin/rajagambar/upload" method="POST" enctype="multipart/form-data">
    @csrf
    <h2>Manual Upload Test</h2>
    <input type="file" name="file" accept="image/*" required>
    <input type="hidden" name="collection" value="test">
    <input type="hidden" name="convert_webp" value="0">
    <button type="submit">Upload</button>
</form>

<script>
document.querySelector('form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/rajagambar/upload', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(data => {
        console.log('Response data:', data);
        alert('Response: ' + data);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error: ' + error);
    });
});
</script>
