<?php

namespace Modules\RajaGambar\Examples;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Modules\RajaGambar\Filament\Forms\Components\RajaGambarUpload;

/**
 * Contoh penggunaan <PERSON>barUpload component
 */
class ExampleForm
{
    /**
     * Contoh form sederhana dengan single image upload
     */
    public static function basicForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Dasar')
                    ->schema([
                        TextInput::make('title')
                            ->label('Judul')
                            ->required(),
                            
                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->rows(3),
                            
                        RajaGambarUpload::make('featured_image')
                            ->label('Gambar Utama')
                            ->collection('featured')
                            ->maxFileSize(5)
                            ->previewSize(200)
                            ->placeholder('Pilih gambar utama untuk artikel')
                            ->required(),
                    ])
            ]);
    }

    /**
     * Contoh form dengan multiple images dan konfigurasi lengkap
     */
    public static function advancedForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Media Management')
                    ->description('Upload dan kelola media untuk konten Anda')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                // Single featured image dengan crop
                                RajaGambarUpload::make('featured_image')
                                    ->label('Gambar Utama')
                                    ->collection('featured')
                                    ->maxFileSize(5)
                                    ->previewSize(200)
                                    ->enableCrop(true)
                                    ->cropAspectRatio(16, 9)
                                    ->cropQuality(90)
                                    ->convertWebp(true)
                                    ->placeholder('Upload gambar utama (16:9)')
                                    ->required(),
                                    
                                // Thumbnail dengan aspect ratio 1:1
                                RajaGambarUpload::make('thumbnail')
                                    ->label('Thumbnail')
                                    ->collection('thumbnails')
                                    ->maxFileSize(2)
                                    ->previewSize(150)
                                    ->enableCrop(true)
                                    ->cropAspectRatio(1, 1)
                                    ->enableResize(true)
                                    ->resizeTo(300, 300)
                                    ->convertWebp(true)
                                    ->placeholder('Upload thumbnail (1:1)'),
                            ]),
                            
                        // Multiple gallery images
                        RajaGambarUpload::make('gallery_images')
                            ->label('Galeri Foto')
                            ->multiple()
                            ->collection('gallery')
                            ->maxFileSize(10)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->previewSize(120)
                            ->perPage(16)
                            ->showFileName(true)
                            ->showFileSize(true)
                            ->convertWebp(true)
                            ->placeholder('Upload multiple gambar untuk galeri'),
                            
                        // Background image dengan watermark
                        RajaGambarUpload::make('background_image')
                            ->label('Background Image')
                            ->collection('backgrounds')
                            ->maxFileSize(15)
                            ->previewSize(250)
                            ->enableResize(true)
                            ->resizeTo(1920, 1080)
                            ->enableWatermark(true)
                            ->watermarkPath('watermark.png')
                            ->watermarkPosition('bottom-right')
                            ->watermarkOpacity(30)
                            ->convertWebp(true)
                            ->placeholder('Upload background image dengan watermark'),
                    ])
            ]);
    }

    /**
     * Contoh form dengan user filtering
     */
    public static function userFilteredForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Media Pribadi')
                    ->description('Kelola media pribadi Anda')
                    ->schema([
                        RajaGambarUpload::make('profile_picture')
                            ->label('Foto Profil')
                            ->collection('profiles')
                            ->byUser(true) // Hanya tampilkan media milik user saat ini
                            ->maxFileSize(3)
                            ->previewSize(150)
                            ->enableCrop(true)
                            ->cropAspectRatio(1, 1)
                            ->convertWebp(true)
                            ->placeholder('Upload foto profil Anda'),
                            
                        RajaGambarUpload::make('personal_gallery')
                            ->label('Galeri Pribadi')
                            ->multiple()
                            ->collection('personal')
                            ->byUser(true)
                            ->maxFileSize(8)
                            ->perPage(12)
                            ->convertWebp(true)
                            ->placeholder('Upload foto ke galeri pribadi'),
                    ])
            ]);
    }

    /**
     * Contoh form dengan konfigurasi minimal (hanya picker)
     */
    public static function pickerOnlyForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pilih dari Galeri')
                    ->schema([
                        RajaGambarUpload::make('selected_image')
                            ->label('Pilih Gambar')
                            ->collection('rajagambar')
                            ->enableUploader(false) // Disable upload, hanya picker
                            ->enablePicker(true)
                            ->previewSize(200)
                            ->placeholder('Pilih gambar dari galeri yang tersedia'),
                            
                        RajaGambarUpload::make('selected_gallery')
                            ->label('Pilih Multiple Gambar')
                            ->multiple()
                            ->collection('rajagambar')
                            ->enableUploader(false)
                            ->enablePicker(true)
                            ->perPage(20)
                            ->placeholder('Pilih beberapa gambar dari galeri'),
                    ])
            ]);
    }

    /**
     * Contoh form dengan konfigurasi upload only
     */
    public static function uploadOnlyForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Upload Baru')
                    ->schema([
                        RajaGambarUpload::make('new_upload')
                            ->label('Upload File Baru')
                            ->collection('uploads')
                            ->enableUploader(true)
                            ->enablePicker(false) // Disable picker, hanya upload
                            ->maxFileSize(20)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif'])
                            ->previewSize(180)
                            ->convertWebp(true)
                            ->placeholder('Upload file gambar baru'),
                            
                        RajaGambarUpload::make('bulk_upload')
                            ->label('Upload Multiple Files')
                            ->multiple()
                            ->collection('bulk')
                            ->enableUploader(true)
                            ->enablePicker(false)
                            ->maxFileSize(15)
                            ->showFileName(true)
                            ->showFileSize(true)
                            ->placeholder('Upload multiple file sekaligus'),
                    ])
            ]);
    }

    /**
     * Contoh form dengan validasi dan rules khusus
     */
    public static function validatedForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Upload dengan Validasi')
                    ->schema([
                        TextInput::make('title')
                            ->label('Judul Konten')
                            ->required()
                            ->maxLength(255),
                            
                        Select::make('category')
                            ->label('Kategori')
                            ->options([
                                'article' => 'Artikel',
                                'gallery' => 'Galeri',
                                'product' => 'Produk',
                            ])
                            ->required(),
                            
                        RajaGambarUpload::make('main_image')
                            ->label('Gambar Utama')
                            ->collection('main')
                            ->maxFileSize(5)
                            ->acceptedFileTypes(['image/jpeg', 'image/png'])
                            ->previewSize(200)
                            ->enableCrop(true)
                            ->cropAspectRatio(4, 3)
                            ->convertWebp(true)
                            ->required()
                            ->placeholder('Upload gambar utama (wajib, max 5MB, format JPG/PNG)'),
                            
                        RajaGambarUpload::make('additional_images')
                            ->label('Gambar Tambahan')
                            ->multiple()
                            ->collection('additional')
                            ->maxFileSize(3)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->previewSize(120)
                            ->perPage(12)
                            ->convertWebp(true)
                            ->placeholder('Upload gambar tambahan (opsional, max 3MB per file)'),
                    ])
            ]);
    }

    /**
     * Contoh form dengan conditional logic
     */
    public static function conditionalForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Konten Dinamis')
                    ->schema([
                        Select::make('content_type')
                            ->label('Tipe Konten')
                            ->options([
                                'single' => 'Single Image',
                                'gallery' => 'Gallery',
                                'slider' => 'Image Slider',
                            ])
                            ->required()
                            ->live(),
                            
                        // Single image - tampil jika content_type = single
                        RajaGambarUpload::make('single_image')
                            ->label('Single Image')
                            ->collection('singles')
                            ->maxFileSize(8)
                            ->previewSize(250)
                            ->enableCrop(true)
                            ->cropAspectRatio(16, 9)
                            ->convertWebp(true)
                            ->visible(fn ($get) => $get('content_type') === 'single')
                            ->required(fn ($get) => $get('content_type') === 'single'),
                            
                        // Gallery - tampil jika content_type = gallery
                        RajaGambarUpload::make('gallery_images')
                            ->label('Gallery Images')
                            ->multiple()
                            ->collection('galleries')
                            ->maxFileSize(6)
                            ->perPage(16)
                            ->convertWebp(true)
                            ->visible(fn ($get) => $get('content_type') === 'gallery')
                            ->required(fn ($get) => $get('content_type') === 'gallery'),
                            
                        // Slider - tampil jika content_type = slider
                        RajaGambarUpload::make('slider_images')
                            ->label('Slider Images')
                            ->multiple()
                            ->collection('sliders')
                            ->maxFileSize(10)
                            ->enableResize(true)
                            ->resizeTo(1920, 800)
                            ->convertWebp(true)
                            ->visible(fn ($get) => $get('content_type') === 'slider')
                            ->required(fn ($get) => $get('content_type') === 'slider'),
                    ])
            ]);
    }
}
