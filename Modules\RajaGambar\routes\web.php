<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\RajaGambar\Http\Controllers\RajaGambarController;
use Mo<PERSON><PERSON>\RajaGambar\Http\Controllers\MediaApiController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group([], function () {
    Route::resource('rajagambar', RajaGambarController::class)->names('rajagambar');
});

// Admin routes for FilamentPHP integration
Route::group(['prefix' => 'admin/rajagambar', 'middleware' => ['web', 'auth']], function () {
    // Media API routes for RajaGambarUpload component
    Route::get('media-api', [MediaApiController::class, 'index'])->name('rajagambar.media-api');
    Route::post('upload', [MediaApiController::class, 'upload'])->name('rajagambar.upload');
    Route::get('media/{id}/preview', [MediaApiController::class, 'preview'])->name('rajagambar.media.preview');
    Route::get('media/{id}', [MediaApiController::class, 'show'])->name('rajagambar.media.show');
    Route::delete('media/{id}', [MediaApiController::class, 'destroy'])->name('rajagambar.media.destroy');
    Route::post('media/bulk', [MediaApiController::class, 'bulk'])->name('rajagambar.media.bulk');
});
