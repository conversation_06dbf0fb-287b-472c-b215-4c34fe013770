<?php
// Test authentication
require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Fake request untuk bootstrap
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

echo "<h1>Test Authentication</h1>";

// Check if user exists
$user = \App\Models\User::where('email', '<EMAIL>')->first();
if ($user) {
    echo "<p>✓ User found: " . $user->email . "</p>";
    
    // Login user
    \Illuminate\Support\Facades\Auth::login($user);
    
    if (\Illuminate\Support\Facades\Auth::check()) {
        echo "<p>✓ User logged in successfully</p>";
        echo "<p>User ID: " . \Illuminate\Support\Facades\Auth::id() . "</p>";
    } else {
        echo "<p>✗ Failed to login user</p>";
    }
} else {
    echo "<p>✗ User not found</p>";
}

// Test CSRF token
$token = csrf_token();
echo "<p>CSRF Token: $token</p>";

// Test route
try {
    $uploadUrl = route('rajagambar.upload');
    echo "<p>✓ Upload route: $uploadUrl</p>";
} catch (Exception $e) {
    echo "<p>✗ Upload route error: " . $e->getMessage() . "</p>";
}

// Test middleware
echo "<h2>Test Middleware</h2>";
try {
    $request = \Illuminate\Http\Request::create('/admin/rajagambar/upload', 'POST');
    $request->setLaravelSession(app('session'));
    
    // Check if route exists
    $route = \Illuminate\Support\Facades\Route::getRoutes()->match($request);
    echo "<p>✓ Route matched: " . $route->getName() . "</p>";
    
    // Check middleware
    $middleware = $route->middleware();
    echo "<p>Middleware: " . implode(', ', $middleware) . "</p>";
    
} catch (Exception $e) {
    echo "<p>✗ Route/Middleware error: " . $e->getMessage() . "</p>";
}

// Test controller
echo "<h2>Test Controller</h2>";
try {
    $controller = app(\Modules\RajaGambar\app\Http\Controllers\MediaApiController::class);
    echo "<p>✓ Controller instantiated</p>";
    
    // Check if method exists
    if (method_exists($controller, 'upload')) {
        echo "<p>✓ Upload method exists</p>";
    } else {
        echo "<p>✗ Upload method not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>✗ Controller error: " . $e->getMessage() . "</p>";
}
?>
