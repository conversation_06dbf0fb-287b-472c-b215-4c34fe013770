{"private": true, "scripts": {"dev": "vite", "build": "vite build", "hmr": "vite --mode hmr"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "axios": "^1.1.2", "browser-sync": "^3.0.3", "concurrently": "^9.1.2", "cropperjs": "^2.0.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "postcss-nesting": "^13.0.1", "tailwindcss": "^3.4.17", "vite": "^6.2.2"}, "type": "module", "dependencies": {"@playwright/test": "^1.53.1", "grapesjs": "^0.22.7", "grapesjs-component-code-editor": "^1.0.20", "hmr": "^0.1.0"}}