<?php

namespace Modules\RajaGambar\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Modules\RajaGambar\Services\SpatieMediaService;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MediaApiController extends Controller
{
    protected SpatieMediaService $mediaService;

    public function __construct(SpatieMediaService $mediaService)
    {
        $this->mediaService = $mediaService;
    }

    /**
     * Get media items for picker
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 12);
        $collection = $request->get('collection', 'default');
        $byUser = $request->boolean('by_user', false);
        $userId = $request->get('user_id');

        $query = Media::query()
            ->where('collection_name', $collection)
            ->orderBy('created_at', 'desc');

        // Filter by user if requested
        if ($byUser && Auth::check()) {
            $query->where('model_id', Auth::id());
        } elseif ($userId) {
            $query->where('model_id', $userId);
        }

        $media = $query->paginate($perPage);

        // Transform data for frontend
        $transformedData = $media->getCollection()->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'file_name' => $item->file_name,
                'mime_type' => $item->mime_type,
                'size' => $item->size,
                'collection_name' => $item->collection_name,
                'url' => $item->getUrl(),
                'original_url' => $item->getUrl(),
                'preview_url' => $item->hasGeneratedConversion('preview') 
                    ? $item->getUrl('preview') 
                    : $item->getUrl(),
                'created_at' => $item->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'data' => $transformedData,
            'current_page' => $media->currentPage(),
            'last_page' => $media->lastPage(),
            'per_page' => $media->perPage(),
            'total' => $media->total(),
        ]);
    }

    /**
     * Upload new media file
     */
    public function upload(Request $request): JsonResponse
    {
        // Get max file size from config (in KB)
        $maxFileSize = config('rajagambar.media.max_file_size', 10240);

        $validator = Validator::make($request->all(), [
            'file' => [
                'required',
                'file',
                'image',
                'max:' . $maxFileSize,
                'mimes:jpeg,jpg,png,gif,webp,svg'
            ],
            'collection' => 'nullable|string|max:255',
            'convert_webp' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            Log::error('RajaGambar upload validation failed', [
                'errors' => $validator->errors(),
                'request_data' => $request->all(),
                'file_info' => $request->hasFile('file') ? [
                    'name' => $request->file('file')->getClientOriginalName(),
                    'size' => $request->file('file')->getSize(),
                    'mime' => $request->file('file')->getMimeType(),
                ] : null,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $file = $request->file('file');
            $collection = $request->get('collection', 'rajagambar');
            $convertWebp = $request->boolean('convert_webp', false);

            // Get or create model instance
            $model = $this->getModelInstance();

            // Add file to media library
            $media = $model->addMediaFromRequest('file')
                ->toMediaCollection($collection);

            // Convert to WebP if requested (this will be handled by registerMediaConversions)
            if ($convertWebp && in_array($file->getMimeType(), ['image/jpeg', 'image/png'])) {
                // WebP conversion will be handled automatically by the model's registerMediaConversions
                Log::info('WebP conversion requested for media', ['media_id' => $media->id]);
            }

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'media' => [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'collection_name' => $media->collection_name,
                    'url' => $media->getUrl(),
                    'original_url' => $media->getUrl(),
                    'preview_url' => $media->hasGeneratedConversion('preview') 
                        ? $media->getUrl('preview') 
                        : $media->getUrl(),
                    'created_at' => $media->created_at->format('Y-m-d H:i:s'),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get media preview
     */
    public function preview($id): \Symfony\Component\HttpFoundation\Response
    {
        try {
            $media = Media::findOrFail($id);
            
            // Check if preview conversion exists
            if ($media->hasGeneratedConversion('preview')) {
                return redirect($media->getUrl('preview'));
            }
            
            // Return original if no preview
            return redirect($media->getUrl());
            
        } catch (\Exception $e) {
            // Return placeholder image
            $placeholderPath = config('rajagambar.raja_gambar_upload.placeholder_image', '/noimage.jpg');
            return response()->file(public_path($placeholderPath));
        }
    }

    /**
     * Get media details
     */
    public function show($id): JsonResponse
    {
        try {
            $media = Media::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'media' => [
                    'id' => $media->id,
                    'name' => $media->name,
                    'file_name' => $media->file_name,
                    'mime_type' => $media->mime_type,
                    'size' => $media->size,
                    'collection_name' => $media->collection_name,
                    'url' => $media->getUrl(),
                    'original_url' => $media->getUrl(),
                    'preview_url' => $media->hasGeneratedConversion('preview') 
                        ? $media->getUrl('preview') 
                        : $media->getUrl(),
                    'conversions' => $media->getGeneratedConversions()->toArray(),
                    'custom_properties' => $media->custom_properties,
                    'created_at' => $media->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $media->updated_at->format('Y-m-d H:i:s'),
                ],
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Media not found',
            ], 404);
        }
    }

    /**
     * Delete media
     */
    public function destroy($id): JsonResponse
    {
        try {
            $media = Media::findOrFail($id);
            
            // Check if user has permission to delete
            if (Auth::check() && $media->model_id !== Auth::id() && !Auth::user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to delete this media',
                ], 403);
            }
            
            $media->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Media deleted successfully',
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete media: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get or create model instance for media
     */
    protected function getModelInstance()
    {
        // Try to get existing RajaGaleri instance for current user
        $modelClass = \Modules\RajaGambar\Models\RajaGaleri::class;
        
        if (Auth::check()) {
            $model = $modelClass::where('user_id', Auth::id())->first();
            
            if (!$model) {
                $model = $modelClass::create([
                    'user_id' => Auth::id(),
                    'name' => 'Media Gallery for ' . Auth::user()->name,
                    'description' => 'Auto-created gallery for user uploads',
                ]);
            }
        } else {
            // Create or get default gallery for guest uploads
            $model = $modelClass::where('user_id', null)->first();
            
            if (!$model) {
                $model = $modelClass::create([
                    'user_id' => null,
                    'name' => 'Default Media Gallery',
                    'description' => 'Default gallery for uploads',
                ]);
            }
        }
        
        return $model;
    }

    /**
     * Bulk operations
     */
    public function bulk(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:delete,move_collection',
            'media_ids' => 'required|array',
            'media_ids.*' => 'integer|exists:media,id',
            'collection' => 'required_if:action,move_collection|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $action = $request->get('action');
            $mediaIds = $request->get('media_ids');
            $collection = $request->get('collection');

            $media = Media::whereIn('id', $mediaIds);

            // Check permissions
            if (Auth::check() && !Auth::user()->hasRole('admin')) {
                $media->where('model_id', Auth::id());
            }

            $mediaItems = $media->get();

            if ($mediaItems->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No media found or unauthorized',
                ], 404);
            }

            switch ($action) {
                case 'delete':
                    $mediaItems->each->delete();
                    $message = 'Media deleted successfully';
                    break;

                case 'move_collection':
                    $mediaItems->each(function ($item) use ($collection) {
                        $item->move($item->model, $collection);
                    });
                    $message = 'Media moved to collection successfully';
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'affected_count' => $mediaItems->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk operation failed: ' . $e->getMessage(),
            ], 500);
        }
    }
}
